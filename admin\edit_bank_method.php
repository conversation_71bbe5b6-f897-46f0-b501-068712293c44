<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 5;
$sub_page = 50;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

$id = $_GET['id'];

if (isset($_REQUEST['submit'])) {

    $bank_name = mysqli_real_escape_string($con, $_REQUEST['bank_name']);
    $ac_no = mysqli_real_escape_string($con, $_REQUEST['ac_no']);
    $ifsc_code = mysqli_real_escape_string($con, $_REQUEST['ifsc_code']);
    $name = mysqli_real_escape_string($con, $_REQUEST['name']);

    $result = dbQuery("UPDATE tabl_rechargesetting SET bank_name='" . $bank_name . "',ac_no='" . $ac_no . "',ifsc_code='" . $ifsc_code . "',name='" . $name . "' WHERE  id='" . $id . "'");

    if ($result) {
        echo '<script>alert("Bank Details Updated!");window.location.href="payment_setting.php"</script>';
    } else {
        echo '<script>alert("Something went wrong!");window.location.href="payment_setting.php"</script>';
    }
}

$sel = dbQuery("SELECT * FROM tabl_rechargesetting WHERE id='" . $id . "'");
$res = dbFetchAssoc($sel);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Update Bank Details
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="recharge_setting.php">Update Bank Details</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info"
                                        enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Update Bank Details</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">

                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Bank Name</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="bank_name" name="bank_name"
                                                                                value="<?php echo $res['bank_name']; ?>"
                                                                                placeholder="Bank Name" required>

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Account No.</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="ac_no" name="ac_no"
                                                                                value="<?php echo $res['ac_no']; ?>"
                                                                                placeholder="Account No" required>

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="fullName">IFSC Code</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="ifsc_code" name="ifsc_code"
                                                                                value="<?php echo $res['ifsc_code']; ?>"
                                                                                placeholder="IFSC Code" required>

                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Name</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="Name" name="name"
                                                                                value="<?php echo $res['name']; ?>"
                                                                                placeholder="Name" required>
                                                                        </div>
                                                                    </div>


                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit"
                                                                                class="btn btn-secondary mb-4 mr-2">Update</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
    CKEDITOR.replace('description');
</script>