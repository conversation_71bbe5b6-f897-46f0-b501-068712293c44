<?php
session_start();
include('../admin/lib/db_connection.php');
$date = date('Y-m-d H:i:s');

//echo $num_rows;

if (isset($_REQUEST['product_id'])) {


    $user_id = $_SESSION['user_id'];
    $product_id = $_REQUEST['product_id'];
    $qty = $_REQUEST['qty'];

    $sel = dbQuery("SELECT * FROM tabl_products WHERE id='" . $product_id . "' ");
    $res = dbFetchAssoc($sel);
    $price = $res['price'];
    $shipping_price = $res['shipping_price'];

    $sel_cart = dbQuery("SELECT * FROM tabl_cart WHERE user_id='" . $user_id . "' AND product_id='" . $product_id . "' ");
    if ($res_cart = dbFetchAssoc($sel_cart)) {
        $cart_qty = $res_cart['qty'] + $qty;
        $total = $cart_qty * $price;

        $result = dbQuery("UPDATE tabl_cart SET qty='$cart_qty',total='$total' WHERE id='" . $res_cart['id'] . "'");
    } else {
        $cat_id = $res['main_cat_id'];
        // $cat_id = $res['main_cat_id'];

        $total = $qty * $price;

        $result = dbQuery("INSERT INTO tabl_cart SET `user_id`='$user_id', product_id='$product_id', cat_id='$cat_id',shipping_price='$shipping_price', qty='$qty',price='$price', total='$total', `date`='$date'");
    }


    if ($result) {
        echo 1;
    } else {
        echo 0;
    }
} else {
    echo '0';
}
