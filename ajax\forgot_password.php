<?php
session_start();
include('../../admin/lib/db_connection.php');

include("../../mail.php");

$sel = dbQuery("SELECT * FROM tabl_user WHERE email='" . $_REQUEST['email'] . "'");
$num = dbNumRows($sel);
if ($num > 0) {
  $res = dbFetchAssoc($sel);
  
  $token = bin2hex(random_bytes(50));
  $link = "https://tracetrade.online/reset_password.php?reset=" . $token;

  $name = $res['name'];

  $to = $_REQUEST['email']; // note the comma
  // Subject
  $subject     = "Trace Trade Reset Password";
  // Message

  $message     = "Thank you for joining us, We hope that you will be happy with be our service.";
  $message2    = "<a href='" . $link . "'>Click Here</a> To Reset Your Password.";

  // To send HTML mail, the Content-type header must be set

  

  $q1 = "UPDATE tabl_user SET token='$token' WHERE email='" . $_REQUEST['email'] . "'";

  $resetpass = dbQuery($q1);

  if ($resetpass) {

    if (sendmail($to, $subject, $message, $message2, $name)) {

      echo 1;
    } else {
      echo 0;
    }
  }else{
    echo 0;
  }

} else {
  echo 2;
}
