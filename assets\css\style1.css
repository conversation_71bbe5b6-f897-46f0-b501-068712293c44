.fullbody {
    background: #f7f8ff;
    height: 100%;
}
.fullbody1 {
    background: #f7f8ff;
    height: 100vh;
}
.logo {
    text-decoration: none;
    color: #0a58ca;
    font-weight: 700;
    letter-spacing: 1px;
    font-size: 20px;
}

.login-btn {
    text-decoration: none;
    color: #0a58ca;
    border: 1px solid #0a58ca;
    border-radius: 4px;
    padding: 3px 11px;
    font-size: 13px;
}

.register-btn {
    text-decoration: none;
    color: white;
    border: 1px solid #0a58ca;
    border-radius: 4px;
    padding: 3px 11px;
    font-size: 13px;
    background: #0a58ca;
}

.carousel-item img {
    border-radius: 10px;
    height: 160px;
}

.alert1 svg {
    height: 25px;
    width: 25px;
    margin-right: 10px;
    color: #0a58ca;
}

.alert1 marquee {
    font-size: 14px;
    font-weight: 600;
}

.alert1 {
    background: white;
    padding: 7px 8px !important;
    border-radius: 50px !important;
    margin: 0px 5px !important;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.popular {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #0a58caa8;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.popular img {
    width: 55%;
}

.popular p {
    margin-bottom: 0px;
    font-size: 15px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
}

.lottery {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #990eea;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.lottery img {
    width: 55%;
}

.lottery p {
    margin-bottom: 0px;
    font-size: 15px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
}

.casino {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #ff9b71;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    height: 65px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.casino img {
    width: 85px;
    position: relative;
    right: 4px;
    top: 0px;
}

.casino p {
    margin-bottom: 0px;
    font-size: 12px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    right: 28px;
    top: -15px;
}

.slots {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #ffc107;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    height: 65px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.slots img {
    width: 85px;
    position: relative;
    right: 4px;
    top: 0px;
}

.slots p {
    margin-bottom: 0px;
    font-size: 12px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    right: 28px;
    top: -15px;
}

.sports {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #ff0018;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    height: 65px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.sports img {
    width: 85px;
    position: relative;
    right: 4px;
    top: 0px;
}

.sports p {
    margin-bottom: 0px;
    font-size: 12px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    right: 28px;
    top: -15px;
}

.rummy {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #0dcaf0;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    height: 65px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.rummy img {
    width: 85px;
    position: relative;
    right: 4px;
    top: 0px;
}

.rummy p {
    margin-bottom: 0px;
    font-size: 12px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    right: 28px;
    top: -15px;
}

.fishing {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #014224;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    height: 65px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.fishing img {
    width: 85px;
    position: relative;
    right: 4px;
    top: 0px;
}

.fishing p {
    margin-bottom: 0px;
    font-size: 12px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    right: 28px;
    top: -15px;
}

.original {
    background-image: url(../img/pattern-png-transparent-4.png);
    background-color: #990eea;
    background-size: contain;
    border-radius: 10px;
    padding: 4px;
    height: 65px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.original img {
    width: 85px;
    position: relative;
    right: 4px;
    top: 0px;
}

.original p {
    margin-bottom: 0px;
    font-size: 12px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    right: 28px;
    top: -15px;
}

.divider {
    height: 20px;
    width: 4px;
    background: #0a58ca;
    margin-right: 10px;
}

.lottery-header .header p {
    font-weight: 700;
    margin-bottom: 0px;
    font-size: 19px;
}

.all-btn a {
    color: black;
    text-decoration: none;
    font-size: 13px;
    border: 1px solid black;
    border-radius: 5px;
    padding: 2px 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.all-btn a svg {
    height: 14px;
    width: 14px;
}

.go-btn {
    color: white;
    text-decoration: none;
    font-size: 13px;
    border: 1px solid white;
    border-radius: 5px;
    padding: 2px 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.lottery-card {
    background: linear-gradient(133deg, #013f9a, #0a58caa6);
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 10px;
}

.lottery-card p {
    margin-top: 20px;
    font-size: 20px;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 0px;
}

.lottery-card img {
    width: 50%;
}

.slotss img {
    width: 100%;
    height: 140px;
    object-fit: cover;
    border-radius: 10px;
}

.alert {
    background: white;
    padding: 7px 8px;
    border-radius: 50px;
    margin: 0px 5px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.profile img {
    width: 30%;
    border-radius: 50%;
    margin-right: 10px;
}

.profile p {
    margin-bottom: 0px;
    font-weight: 600;
}

.receive .rec-amt {
    display: flex;
    flex-direction: column;
}

.receive .rec-amt p {
    margin-bottom: 0px;
    text-align: center;
}

.rec {
    font-weight: 700;
}

.win {
    font-size: 13px;
    color: grey;
    font-weight: 500;
}

.alert {
    padding: 5px !important;
}

.receive .rec-amt .rec1 {
    letter-spacing: 1px;
    margin-bottom: 0px;
    text-align: center;
    background: linear-gradient(45deg, #0a58ca, #0a58ca4f);
    color: white;
    border-radius: 10px;
}

.fs-12 {
    font-size: 12px;
}

.gap-10 {
    gap: 10px;
}

.alert p svg {
    fill: #0a58ca;
}

.color-blue {
    color: #0a58ca;
}

.alert-head p {
    font-size: 23px;
    font-weight: 700;
    color: #0a58ca;
    letter-spacing: 1px;
}

.svg-demo {
    width: 45px;
    height: 45px;
    background: #0a58ca;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.svg-demo svg {
    color: white;
    height: 20px;
    width: 20px;
}

.lang-svg {
    font-size: 14px;
    font-weight: 500;
    color: black;
}

.language {
    border-bottom: 1px solid grey;
    padding-bottom: 10px;
}

.lang-svg svg {
    width: 18px;
    height: 18px;
    color: #0a58ca;
    margin-right: 10px;
}

.navigation {
    position: relative;
    width: 100%;
    height: 70px;
    background: #98bcf1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px 10px 0px 0px;
}

footer {
    position: fixed;
    bottom: 0;
    width: 100%;
}

.navigation ul {
    display: flex;
    width: 350px;
    margin-bottom: 0px;
    padding: 0;
}

.navigation ul li {
    position: relative;
    list-style: none;
    width: 70px;
    height: 70px;
    z-index: 1;
}

.navigation ul li a {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    text-align: center;
    font-weight: 500;
}

.navigation ul li a .icon {
    position: relative;
    display: block;
    line-height: 75px;
    font-size: 1.75em;
    text-align: center;
    transition: 0.5s;
    color: #0a58ca;
}

.navigation ul li.active a .icon {
    transform: translateY(-32px);
    color: white;
}

.navigation ul li a .text {
    position: absolute;
    font-weight: 500;
    letter-spacing: 0.05em;
    font-size: 1em;
    transition: 0.5s;
    color: #0a58ca;
    opacity: 0;
    transform: translateY(20px);
}

.navigation ul li.active a .text {
    opacity: 1;
    transform: translateY(10px);
}

.indicator {
    position: absolute;
    top: -50%;
    width: 70px;
    height: 70px;
    background: #0a58ca;
    border-radius: 50%;
    border: 6px solid #f7f8ff;
    transition: 0.5s;
}

/* .indicator::before{
    content: '';
    position: absolute;
    top: 50%;
    left: -22px;
    width: 20px;
    height: 20px;
    background: transparent;
    border-top-right-radius: 20px;
    box-shadow: 0px -10px 0 0 white;
} */
.mb-120 {
    margin-bottom: 120px;
}

/* .indicator::after{
    content: '';
    position: absolute;
    top: 50%;
    right: -22px;
    width: 20px;
    height: 20px;
    background: transparent;
    border-top-left-radius: 20px;
    box-shadow: 0px -10px 0 0 white;
} */
.navigation ul li:nth-child(1).active~.indicator {
    transform: translateX(calc(70px * 0));
}

.navigation ul li:nth-child(2).active~.indicator {
    transform: translateX(calc(70px * 1));
}

.navigation ul li:nth-child(3).active~.indicator {
    transform: translateX(calc(70px * 2));
}

.navigation ul li:nth-child(4).active~.indicator {
    transform: translateX(calc(70px * 3));
}

.navigation ul li:nth-child(5).active~.indicator {
    transform: translateX(calc(70px * 4));
}

.register-header {
    padding: 10px;
    background: linear-gradient(45deg, #f44336, #f4433694);
}

.register-header svg {
    color: white;
    height: 20px;
    width: 20px;
}

.register-header p {
    color: white;
    text-align: start;
    letter-spacing: 1px;
    font-size: 20px;
    font-weight: 700;
}

.register {
    padding: 20px;
    background: linear-gradient(45deg, #0a58ca, #0a58ca94);
}

.reg-head p {
    color: white;
    letter-spacing: 1px;
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 3px;
}

.reg-heade p {
    color: white;
    font-size: 13px;
}

.reg-btn {
    background: transparent !important;
    color: grey !important;
    font-size: 15px !important;
    border-bottom: 1px solid grey !important;
    border-radius: 0px !important;
}

.reg-btn.active {
    background: transparent !important;
    color: #0a58ca !important;
    font-size: 15px !important;
    border-bottom: 1px solid #0a58ca !important;
    border-radius: 0px !important;
}

.reg-btn svg {

    color: grey !important;

}

.reg-btn.active svg {

    color: #0a58ca !important;

}

.reg svg {
    margin-top: 20px;
    height: 25px;
    width: 25px;
    color: #0a58ca;
}

.reg-input input {
    font-size: 14px;
    padding: 10px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}

.reg-input label {
    font-size: 16px;
    font-weight: 500;
}

.reg-input label svg {
    color: #0a58ca;
}

.reg-input .register-btn {
    width: 100%;
    padding: 8px;
    font-size: 18px;
    text-align: center;
    border-radius: 50px;
    font-weight: 600;
    letter-spacing: 1px;
}

.reg-input .login-btn {
    width: 100%;
    padding: 8px;
    font-size: 18px;
    text-align: center;
    border-radius: 50px;
    font-weight: 600;
    letter-spacing: 1px;
}

.bt {
    padding: 5px !important;
    font-size: 15px !important;
}

.forget-icon ion-icon {
    font-size: 27px;
    background: linear-gradient(45deg, #0e5bcb, #105ccb63);
    color: white;
    padding: 10px;
    border-radius: 50%;
    border: 1px solid #0a58ca;
}

a {
    text-decoration: none !important;

}

.forget-icon p {
    color: black;
    text-decoration: none;
    margin-top: 7px;
    font-size: 17px;
    font-weight: 500;
}

.center {
    font-weight: 500;
}

.setting-icon svg {
    color: #0a58ca;
    height: 20px;
    width: 20px;
}

.set p {
    color: black;
    font-size: 13px;
    text-align: center;
}
.bet-icon svg{
    width: 30px;
    height: 30px;
    margin-right: 10px;
}
.bet{
    margin-bottom: 0px;
    color: black;
    font-weight: 500;
}
.bett{
    margin-bottom: 0px;
    color: black;
    /* font-weight: 500; */
    font-size: 12px;
}
.alert-safe{
    background: linear-gradient(
45deg
, #0a58ca, #0dcaf0);
}
.ludo svg{
    height: 35px;
    width: 35px;
    fill: #f8f9fa;
    margin-right: 10px;
}
.safe1{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.safe-header p{
    
    margin-bottom: 0px;
    color: white;
    font-weight: 500;
    letter-spacing: 1px;

}
.safe-price{
    display: flex;
    align-items: center;
    color: white;
    font-size: 15px;
    background: #0a58ca6e;
    padding: 1px 5px;
    border-radius: 5px;
}
.safe2 p{
    color: white;
    margin-bottom: 0px;
    font-size: 13px;
}
.profile1{
    height: 200px;
    background: linear-gradient(
45deg
, #0a58ca, #0dcaf0);
    border-radius: 0px 0px 50px 50px;
}
.profile-id{
    display: flex;
    align-items: center;
    padding: 30px;
}
.profile-image{
    display: contents;
}
.profile-image img{
    width: 22%;
    border-radius: 50%;
    margin-right: 10px;
}
.pro-name p{
    margin-bottom: 0px;
    font-weight: 600;
    color: white;
    font-size: 15px;
}
.uid{
    font-size: 12px;
    color: white;
    background: #0a58ca8c;
    display: inline;
    padding: 4px 10px;
    border-radius: 20px;
    /* display: flex; */
    align-items: center;
}
.uid span svg{
    width: 13px;
    height: 13px;
}
.last-login p{
    margin-bottom: 0px;
    font-weight: 400;
    color: white;
    font-size: 12px;
}
.bal-alert{
    
    position: relative !important;
    top: -70px;
    background: #f7f8ff !important;
    margin: 0px 20px;
}
.bal p{
    font-size: 15px;
    margin-bottom: 3px;
    font-weight: 500;
    color: grey;
}
.total-balance{
    border-bottom: 1px solid grey;
    padding-bottom: 10px;
    margin-bottom: 15px;
}
.bal1{
    font-size: 19px;
    color: black;
    font-weight: 700;
}
.bal1 svg{
    color: grey;
}
.wallet{
    display: flex;
    flex-direction: column;
    align-items: center;
}
.wallet svg{
    width: 25px;
    height: 25px;
    color: #0a58ca;
}
.wallet p{
    margin-bottom: 0px;
    margin-top: 5px;
    font-size: 15px;
    font-weight: 500;
    color: black;
}
.top-75{
top: -75px;
position: relative;
}
.bal-alert1{
        position: relative !important;
        top: -12px;
        background: #f7f8ff !important;
        margin: 0px 20px;
}
.wallet-circle{
    height: 80px;
    width: 80px;
    background: linear-gradient(
45deg
, #0a58ca, #0dcaf0);
    border-radius: 50%;
    position: relative;
}
.wallet-circle2{
    height: 80px;
    width: 80px;
    background: grey;
    border-radius: 50%;
    position: relative;
}
.wallet-circle1{
    
        height: 64px;
        width: 64px;
        background: #f7f8ff;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
}
.price{
    font-size: 16px;
    margin-bottom: 0px;
    font-weight: 500;
    margin-top: 10px;
}
.price-text{
    font-size: 13px;
    margin-bottom: 0px;
    font-weight: 500;

    color: grey;
}
.butt a{
    width: 100%;
    text-align: center;
    padding: 5px 15px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 1px;
    border-radius: 20px;
}
.wallet2 svg {
    width: 50px;
    height: 50px;
    color: #0a58ca;
    background: white;
    padding: 11px;
    border-radius: 10px;
    box-shadow: rgb(8 88 201 / 27%) 0px 3px 15px;
}
.wallet2 p {
    margin-bottom: 0px;
    margin-top: 5px;
    font-size: 13px;
    font-weight: 500;
    color: black;
    text-align: center;
}
.lott{
    
        background: linear-gradient(
    182deg
    , #0a58ca, #0dcaf0);
        padding: 17px;
  
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        border-radius: 10px;
    
}
.lott p{
    color: white;
    font-size: 14px;
    margin-bottom: 0px;
    font-weight: 500;
}
.language1 .lang-svg svg {
    width: 30px;
    height: 30px;
    color: white;
    margin-right: 10px;
    background: linear-gradient(45deg, #0a58ca, #0dcaf0);
    padding: 5px;
    border-radius: 50%;
}
.bal-alert1 {
    position: relative !important;
    top: -180px;
    background: #f7f8ff !important;
    margin: 0px 20px;
}
.fs-13{
    font-size: 13px;
}
.ml-2{
    margin-left: 10px;
}
.display-contents{
    display: contents;
}
.top-150{
    top: -150px;
    position: relative;
}
.login-noti{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.login-svg{
    display: flex;
    align-items: center;
    gap: 10px;
}
.login-svg svg{
    color: #0a58ca;
    width: 20px;
    height: 20px;
}
.login-svg p{
    margin-bottom: 0px;
    font-weight: 500;
}
.login-del svg{
    color: #0a58ca;
    width: 18px;
    height: 18px;
}
.login-time{
    font-size: 13px;
    color: grey;
    font-weight: 600;
    margin-top: 3px;
    margin-bottom: 10px;
}
.login-data{
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 0px;
}
.deposit p{
    margin-bottom: 0px;
    background: #34be8a;
    color: white;
    font-weight: 500;
    font-size: 14px;
    padding: 2px 10px;
    border-radius: 5px;
}
.withdraw p{
    margin-bottom: 0px;
    background: #ff7374;
    color: white;
    font-weight: 500;
    font-size: 14px;
    padding: 2px 10px;
    border-radius: 5px;
}
.red{
    color: #ff7374 !important;
}
.deposit-complete p{
    margin-bottom: 0px;
    font-size: 13px;
    font-weight: 500;
    color: grey;
    margin-right: 5px;
}
.deposit-complete svg{
   width: 14px;
   height: 14px;
}
.deposit-border{
    
    border-bottom: 1px solid #80808094;
    padding-bottom: 10px;

}
.balance p{
    font-size: 13px;
    font-weight: 500;
    color: grey;
    letter-spacing: 0.25px;
    margin-bottom: 0px;
}
.balance textarea{
    border: 1px solid grey;
    padding: 5px 10px;
    font-size: 14px;
    border-radius: 5px;
}
.withdraw1 p{
    margin-bottom: 0px;
    background: #0a58ca;
    color: white;
    font-weight: 500;
    font-size: 15px;
    padding: 7px 10px;
    border-radius: 5px;
}
.red1{
    color: #ff7374 !important;
    font-size: 18px !important;
    font-weight: 700 !important;
}
.green{
    color: green !important;
    font-size: 18px !important;
    font-weight: 700 !important;
}
.register-header a p {
    color: white;
    text-align: end;
    letter-spacing: 1px;
    font-size: 13px;
    font-weight: 400;
 margin-bottom: 0px;
}
.feedback-textarea{
    width: 100%;
    border: none;
    font-size: 13px;
}
.send{
    text-align: center;
    font-size: 14px;
    margin-bottom: 0px;
}
.submit-btn {
    text-decoration: none;
    color: white;
    border: 1px solid #0a58ca;
    border-radius: 50px;
    padding: 8px 11px;
    font-size: 15px;
    background: #0a58ca;
    text-align: center;
    font-weight: 500;
    letter-spacing: 1px;
}
.alert-bg{
    background: linear-gradient(
        45deg
        , #0a58ca, #0dcaf0);
}
.balance-svg{
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    margin-left: 20px;
    margin-top: 10px;
}
.balance-svg svg{
    color: white;
    height: 18px;
    width: 18px;
}
.balance-svg p{
    margin-bottom: 0px;
    color: white;
}
.balance-svg1{
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    margin-left: 20px;
    margin-top: 10px;
}
.balance-svg1 p{
    margin-bottom: 0px;
    color: white;
    font-size: 24px;
    font-weight: 700;
}
.balance-svg1 svg{
    color: white;
    height: 22px;
    width: 22px;
}
.deposit-amount{
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}
.deposit-amount svg{
    width: 20px;
    height: 20px;
    color: #0a58ca;
}
.deposit-amount p{
    margin-bottom: 0px;
    font-size: 18px;
    font-weight: 700;
}
.deposit-price{
    border: 1px solid #0a58ca;
    text-align: center;
    padding: 2px;
    border-radius: 5px;
    color: #0a58ca;
    font-weight: 500;
}
.dep .active{
    color: white;
    background: #0a58ca;
}
.dep input{
    margin-top: 20px;
    width: 100%;
    padding: 10px 15px;
    border-radius: 10px;
    border: none;
    background: #f6f7ff;
}

.recharge1 svg{
    color: #0a58ca;
    width: 14px;
    height: 14px;
}
.recharge1{
    font-size: 12px;
}
.promotion-page .promotion-mian[data-v-122fe35d] {
    padding-top: 0.4rem;
}
.promotion-page .promotion-mian .promotion-mian__title[data-v-122fe35d] {
    text-align: center;
    margin-bottom: 0.66667rem;
}
.promotion-page .promotion-mian .promotion-mian__title h1[data-v-122fe35d] {
    margin-bottom: 7px;
    color: #135ecc;
    font-size: 20px;
    line-height: 35px;
    font-weight: 600;
}
.promotion-page .promotion-mian .promotion-mian__title p[data-v-122fe35d] {
    color: #666;
    font-size: 14px;
}
.promotion-page .promotion-mian .promotion-box[data-v-122fe35d] {
    position: relative;
    padding: 19px;
    border: 1px solid #135ecc;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    background: #fff;
    box-shadow: 0 0.05333rem 0.21333rem #d0d0ed5c;
    margin-bottom: 0.66667rem;
    margin: 0px 10px 20px 10px;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle[data-v-122fe35d] {
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translate(-50%);
    width: 100%;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:first-of-type {
    left: -0.01333rem;
    border-top-left-radius: 16px;
    border-top: 4px solid #135ecc;
    border-left: 4px solid #135ecc;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:first-of-type, .promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:last-of-type {
    width: 0.68rem;
    height: 0.68rem;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d] {
    position: absolute;
    top: 0;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:first-of-type:after {
    left: 10px;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:first-of-type:after, .promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:last-of-type:after {
    content: "";
    position: absolute;
    top: 10px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #135ecc;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:last-of-type {
    right: 0;
    border-top-right-radius: 16px;
    border-top: 4px solid #135ecc;
    border-right:4px  solid #135ecc;
 
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:first-of-type, .promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d]:last-of-type {
    width: 1.68rem;
    height: 1.68rem;
}
.promotion-page .promotion-mian .promotion-box__borderTopStyle span[data-v-122fe35d] {
    position: absolute;
    top: 0;
}
.promotion-page .promotion-mian .promotion-mian__title h1[data-v-122fe35d] {
    margin-bottom: 7px;
    color: #135ecc;
    font-size: 20px;
    line-height: 35px;
    font-weight: 600;
}
.promotion-page .promotion-mian .promotion-box__titleLeft[data-v-122fe35d] {
    left: calc(50% - 3.2rem);
    -webkit-transform: translateX(-50%);
    transform: translate(-50%);
}
.promotion-page .promotion-mian .promotion-box__titleLeft[data-v-122fe35d], .promotion-page .promotion-mian .promotion-box__titleRight[data-v-122fe35d] {
    position: absolute;
    top: -0.26667rem;
    width: 0.26667rem;
    height: 0.53333rem;
    background-color: #135ecc;
    -webkit-clip-path: polygon(50% 0%, 100% 0%, 50% 50%, 100% 100%, 50% 100%, 0% 50%);
    clip-path: polygon(50% 0%, 100% 0%, 50% 50%, 100% 100%, 50% 100%, 0% 50%);
    z-index: 5;
}
.promotion-page .promotion-mian .promotion-box .promotion-title[data-v-122fe35d] {
    position: absolute;
    top: -10px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translate(-50%);
    width: 81px;
    height: 20px;
    color: #fff;
    font-size: 17px;
    text-align: center;
    line-height: 19px;
    background-color: #135ecc;
    background-color: #135ecc;
    -webkit-clip-path: polygon(7% 0%, 93% 0%, 100% 50%, 93% 100%, 7% 100%, 0% 50%);
    clip-path: polygon(7% 0%, 93% 0%, 100% 50%, 93% 100%, 7% 100%, 0% 50%);
}
.promotion-page .promotion-mian .promotion-box__titleRight[data-v-122fe35d] {
    left: calc(50% + 3.2rem);
    -webkit-transform: translateX(-50%) rotate(180deg);
    transform: translate(-50%) rotate(180deg);
}
.promotion-page .promotion-mian .promotion-box__titleLeft[data-v-122fe35d], .promotion-page .promotion-mian .promotion-box__titleRight[data-v-122fe35d] {
    position: absolute;
    top: -10px;
    width: 12px;
    height: 20px;
    background-color: #135ecc;
    -webkit-clip-path: polygon(50% 0%, 100% 0%, 50% 50%, 100% 100%, 50% 100%, 0% 50%);
    clip-path: polygon(50% 0%, 100% 0%, 50% 50%, 100% 100%, 50% 100%, 0% 50%);
    z-index: 5;
}
.promotion-page .promotion-mian .promotion-box .promotion-txt[data-v-122fe35d] {
    color: #666;
    font-size: 13px;
    line-height: 22px;
}
.promotion-page .promotion-mian .promotion-grade[data-v-122fe35d] {
    overflow: hidden;
    margin-bottom: 20px !important;
    margin: 10px;
}
.promotion-page .promotion-mian .promotion-grade-th[data-v-122fe35d] {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    height: 42px;
    color: #fff;
    font-size: 14px;
    line-height: 39px;
    background: #135ecc;
    text-align: center;
}
.promotion-page .promotion-mian .promotion-grade-th .item[data-v-122fe35d] {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}
.promotion-page .promotion-mian .promotion-grade-tr[data-v-122fe35d] {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    width: calc(100% + 0.01333rem);
    background-color: #fff;
    margin: 0 -0.01333rem;
    color: #666;
}
.promotion-page .promotion-mian .promotion-grade-tr .item[data-v-122fe35d] {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    width: 33.3%;
    height: 1.93333rem;
    padding: 0.06667rem 0;
    border: 0.01333rem solid #135ecc;
    font-size: 16px;
    margin-bottom: -0.01333rem;
    margin-right: -0.01333rem;
}
.icon-LV[data-v-122fe35d] {
    height: 0.61333rem;
    width: 1.33333rem;
    background: url(/assets/png/lv-450d4246.png) no-repeat center center;
    background-size: cover;
    position: relative;
    text-align: center;
}
.promotion-page .promotion-mian .promotion-content[data-v-122fe35d] {
    width: 100%;
    margin-bottom: 0.53333rem;
    overflow: hidden;
    margin: 10px;
}
.promotion-page .promotion-mian .promotion-content_title[data-v-122fe35d] {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    margin-bottom: 0.28rem;
    font-size: 17px;
    font-weight: 600;
}
.promotion-page .promotion-mian .promotion-content_title[data-v-122fe35d]:before {
    content: "";
    display: block;
    width: 4px;
    height: 17px;
    margin-right: 8px;
    background-color: #135ecc;
}
.promotion-page .promotion-mian .promotion-content .promotion-list__container[data-v-122fe35d] {
    margin-bottom: 0.75rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
}
.promotion-page .promotion-mian .promotion-content .promotion-list__container-item[data-v-122fe35d] {
    font-size: 15px;
    text-align: center;
    width: 100%;
}
.promotion-page .promotion-mian .promotion-content .promotion-list__container-item_title[data-v-122fe35d] {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    background: #135ecc;
}
.promotion-page .promotion-mian .promotion-content .promotion-list__container-item_title span[data-v-122fe35d] {
    display: block;
    width: 100%;
    min-height: 1.33333rem;
    color: #fff;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    padding: 0 0.13333rem;
}
.promotion-page .promotion-mian .promotion-content .promotion-list__container-item__content[data-v-122fe35d] {
    background: #fff;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}
.promotion-page .promotion-mian .promotion-content .promotion-list__container-item__content>div[data-v-122fe35d] {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    width: 100%;
    height: 29px;
    border-top: 0.01333rem solid #135ecc;
    border-left: 0.01333rem solid #135ecc;
}
.trans{
    color: white;
    margin-left: 20px;
    font-size: 13px;
}
.finan p{
    margin-bottom: 0px;
    color: white;
    font-size: 11px;
}
.finan svg{
    color: white;
    height: 14px;
    width: 14px;
    margin-right: 10px;
}
.finan{
    border: 1px solid white;
    padding: 3px;
    border-radius: 5px;
}
.StrongBox__container-income[data-v-3d263761] {
    margin-top: 0.26667rem;
    min-height: 5.13333rem;
    background: #ffffff;
    box-shadow: 0 0.05333rem 0.21333rem #d0d0ed5c;
    border-radius: 0.13333rem;
    padding: 0.4rem 0.18667rem 0.10667rem;
}
.StrongBox__container-income-header[data-v-3d263761] {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}
.StrongBox__container-income-header-left[data-v-3d263761], .StrongBox__container-income-header-right[data-v-3d263761] {
    text-align: center;
    padding-top: 0.12rem;
    width: 50%;
    padding: 0.12rem 0.26667rem 0;
}
.StrongBox__container-income-header-left-num[data-v-3d263761] {
    color: #135ecc;
    height: 14px;
    line-height: .48rem;
    font-size: 20px;
}
.StrongBox__container-income-header-left-text[data-v-3d263761], .StrongBox__container-income-header-right-text[data-v-3d263761] {
    height: 1.37333rem;
    font-size: 13px;
    color: #aeb0c6;
    margin: 0.16rem 0;
}
.StrongBox__container-income-header-left-myrale[data-v-3d263761], .StrongBox__container-income-header-right-myrale[data-v-3d263761] {
    height: 1.48rem;
    border: 1px solid #dbdbdb;
    border-radius: 0.53333rem;
    min-width: 2.93333rem;
    font-size: 14px;
    color: #a8abc0;
    margin: auto;}
.StrongBox__container-income-header-right[data-v-3d263761] {
    border-left: 0.01333rem solid #daddf0;
}
.StrongBox__container-income-header-right-num[data-v-3d263761] {
     color: #333; 
     height: 14px;
     line-height: 11px;
     font-size: 20px;
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap;
}
.StrongBox__container-income-buttom[data-v-3d263761] {
    margin-top: 0.33333rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}
.StrongBox__container-income-buttom>div[data-v-3d263761] {
    width: 49%;
    height: 1.93333rem;
    line-height: 1.93333rem;
    border: 0.01333rem solid #135ecc;
    border-radius: 0.13333rem;
    font-size: 15px;
    font-weight: 400;
    color: #135ecc;
    text-align: center;
}
.StrongBox__container-income-buttom>div+div[data-v-3d263761] {
    background: #135ecc;
    color: #fff;
}
.StrongBox__container-income-tip[data-v-3d263761] {
    font-size: 11px;
    color: #135ecc;
    margin-top: 0.33333rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    text-align: left;
}
.StrongBox__container-errorTip[data-v-3d263761] {
    width: 16px;
    height: 16px;
    margin-right: 0.12rem;
}
.StrongBox__container-income-godetail[data-v-3d263761] {
    text-align: center;
    height: 0.4rem;
    line-height: .4rem;
    font-size: .32rem;
    color: #888;
    margin-top: 0.46667rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}
.lot p{
    color: grey;
    font-size: 13px;
    margin-bottom: 0px;
}
.round1{
    width: 30px;
    height: 30px;
    background: lightgray;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid;
    font-size: 13px;
}
.text-gray{
    color: grey;
}
.red2{
    width: 30px;
    height: 30px;
    background: #1d65ce;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid;
    font-size: 13px;
    color: white;
}
.round3{
    color: black;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    font-size: 20px;
}
.one{
    width: 18px;
    height: 18px;
    border: 1px solid;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.one1{
    width: 18px;
    height: 18px;
    border: 1px solid;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    background: #0a58ca;
    color: white;
    border-color: #0a58ca;
}


.vipbox{
    background: linear-gradient(304deg, #0a58ca, #6a99e0);
    padding: 10px;
    border-radius: 5px;
}
.vipbox1{
    background: linear-gradient(323deg, #570000, #f1283b);
    padding: 10px;
    border-radius: 5px;
}
.vipbox2{
    background: linear-gradient(323deg, #006436, #14d47b);
    padding: 10px;
    border-radius: 5px;
}


.vip1 span:nth-child(2){
    color: #ffc107;
    font-size: 32px;
    font-family: none;
    font-weight: 600;
}
.vip1 svg{
    height: 30px;
    width: 30px;
    color: #ffc107;
}
.vip1{
    gap: 10px;
}
.tick svg{
    height: 20px;
    width: 20px;
    color: #198754;
    background: #35d935;
    border-radius: 50%;
    padding: 2px;
}
.tick p{
    margin-bottom: 0px;
    margin-left: 6px;
    color: #35d935;
}
.cus p{
    color: white;
    border: 1px solid;
    display: inline;
    padding: 2px;
    border-radius: 5px;
    font-size: 12px;
}
.vip2 p{
    margin: 10px 0px 0px 0px;
    color: white;
    font-size: 13px;

}
.per1{
    color: white;
    background: #1b63ce;
    padding: 0px 11px;
    font-size: 14px;
    border-radius: 10px;
    margin-bottom: 0px;
}
.per{
    color: white;


    font-size: 14px;

    margin-bottom: 0px;
}
.vip4 p {
    margin: 34px 0px 40px 0px;
    color: white;
    font-size: 20px;
}


.daily{
    font-weight: 500;
    margin-bottom: 5px;
}
.dailybet{
    display: flex;
    gap: 10px;
    align-items: center;
}
.dailybet p{
    font-size: 13px;
    margin-bottom: 10px;
}
.blue{
    font-size: 13px;
    margin-bottom: 10px;
    color: #145ecc;
    font-weight: 500;
}
.win .img{
    width: 15%;
    gap: 15px;
    margin-bottom: 15px;
}
.win .img img{
    border-radius: 50%;
}
.set{
    background: #8080802e;
    padding: 5px;
    border-radius: 5px;
}
.set p{
    text-align: left;
}
.blues{
    color: #0a58ca !important;
}
.total{
    font-size: 15px;
    font-weight: 600;
}
.real{
    display: flex;
    gap: 10px;
    border: 1px solid #1a63ce;
    padding: 4px 10px;
    width: fit-content;
    border-radius: 5px;
    margin-top: 10px;
}
.real svg{
    color: #115dcb;
    width: 18px;
    height: 18px;
}
.real p{
    color: #115dcb;
    margin-bottom: 0px;

}
.cash{
    margin: 6px 0px;
    font-weight: 600;
}
.cash svg{
    color: #1962cd;
    margin-right: 6px;
}
.upgradebet{
    font-size: 13px;
    margin-bottom: 10px;
}
.w-49{
    width: 49%;
}
.totals{
    font-size: 13px;
    margin-bottom: 10px;
}