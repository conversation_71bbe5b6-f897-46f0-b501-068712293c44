<?php
ob_start();
session_start();
if ($_SESSION['user_id'] == "") {
    header("location:login.php");
    exit();
}
// echo $_SESSION['user_id'];

include ('../../admin/lib/db_connection.php');
include ('../lib/gamesetting.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');


// Function to send JSON response
function json_response($data, $isSuccess, $message)
{
    $res = array(
        "data" => $data,
        "isSuccess" => $isSuccess,
        "message" => $message
    );
    return json_encode($res);
}

// Function to fetch user details


// function new_game_generated($r)
function new_game_generated()
{
    // Update Setting
    $sql_update_setting = dbQuery("UPDATE tabl_aviator_settings SET `value` = '0' WHERE category = 'game_status'");

    // Set session variable
    $_SESSION['gamegenerate'] = '1';

    // Get current id
    $current_id = currentid();

    // Return JSON response
    return json_encode(array("id" => $current_id), JSON_PRETTY_PRINT);
}

echo new_game_generated();