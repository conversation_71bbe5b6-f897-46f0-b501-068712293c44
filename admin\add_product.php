<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 5;
$sub_page = 50;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['submit'])) {


	if ($_FILES["image"]["name"] != "") {

		$target_dir = "../assets/img/products/";
		$name = rand(10000, 1000000);
		$extension = pathinfo($_FILES["image"]["name"], PATHINFO_EXTENSION);
		$new_name = $name . "." . $extension;
		$target_file = $target_dir . $name . "." . $extension;

		$imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
		if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
			die("This is not valid image. Please try again.");
		} else {
			move_uploaded_file($_FILES["image"]["tmp_name"], $target_file);
			$target_path = "../assets/img/products/" . $new_name;
			$resizeObj = new resize("../assets/img/products/" . $new_name);
			$resizeObj->resizeImage(145, 187, 'exact');
			$resizeObj->saveImage("../assets/img/products/thumb-100/" . $new_name, 100);

			$resizeObj = new resize("../assets/img/products/" . $new_name);
			$resizeObj->resizeImage(151, 199, 'exact');
			$resizeObj->saveImage("../assets/img/products/thumb-150/" . $new_name, 100);

			$resizeObj = new resize("../assets/img/products/" . $new_name);
			$resizeObj->resizeImage(518, 684, 'exact');
			$resizeObj->saveImage("../assets/img/products/thumb-500/" . $new_name, 100);


			// echo "INSERT INTO tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',cat_id='" . $_REQUEST['cat_id'] . "',sub_cat_id='" . $_REQUEST['sub_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',image='" . $new_name . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',status=1,date_added='" . $date . "'";

			// dbQuery("INSERT INTO tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',cat_id='" . $_REQUEST['cat_id'] . "',sub_cat_id='" . $_REQUEST['sub_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',image='" . $new_name . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',status=1,date_added='" . $date . "'");
			dbQuery("INSERT INTO tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',image='" . $new_name . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',status=1,date_added='" . $date . "'");
			$last = dbInsertId();
		}
	} else {

		// dbQuery("INSERT INTO tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',cat_id='" . $_REQUEST['cat_id'] . "',sub_cat_id='" . $_REQUEST['sub_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',status=1,date_added='" . $date . "'");
		dbQuery("INSERT INTO tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',status=1,date_added='" . $date . "'");
		$last = dbInsertId();
	}
	$stock = dbQuery("INSERT INTO tabl_stock SET p_id='" . $last . "',in_stock='" . $_REQUEST['qty'] . "'");


	dbQuery("INSERT INTO tabl_product_filter SET p_id='" . $last . "',sub_cat_id='" . $_REQUEST['main_cat_id'] . "',price='" . $_REQUEST['price'] . "',availablity=availablity+'" . $_REQUEST['qty'] . "'");


	$num = $_REQUEST['num'] - 1;

	for ($i = 1; $i <= $num; $i++) {
		if ($_FILES["images_" . $i]["name"] != "") {

			$target_dir = "../assets/img/products/";
			$name = rand(10000, 1000000);
			$extension = pathinfo($_FILES["images_" . $i]["name"], PATHINFO_EXTENSION);
			$new_name1 = $name . "." . $extension;
			$target_file = $target_dir . $name . "." . $extension;

			$imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
			if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
				die("This is not valid document. Please try again.");
			} else {
				move_uploaded_file($_FILES["images_" . $i]["tmp_name"], $target_file);

				$target_path = "../assets/img/products/" . $new_name1;
				$resizeObj = new resize("../assets/img/products/" . $new_name1);
				$resizeObj->resizeImage(145, 187, 'exact');
				$resizeObj->saveImage("../assets/img/products/thumb-100/" . $new_name1, 100);

				$resizeObj = new resize("../assets/img/products/" . $new_name1);
				$resizeObj->resizeImage(151, 199, 'exact');
				$resizeObj->saveImage("../assets/img/products/thumb-150/" . $new_name1, 100);

				$resizeObj = new resize("../assets/img/products/" . $new_name1);
				$resizeObj->resizeImage(518, 684, 'exact');
				$resizeObj->saveImage("../assets/img/products/thumb-500/" . $new_name1, 100);

				$qry_detail = dbQuery("insert into tabl_product_images set 										
										p_id='" . $last . "',								
										image='" . $new_name1 . "'");
			}
		}
	}

	$color_num = $_REQUEST['color_num'] - 1;
	$col_arary = array();
	for ($i = 1; $i <= $color_num; $i++) {
		if ($_REQUEST['color_' . $i] != "") {
			$qry_detail = dbQuery("insert into tabl_product_options set 										
										p_id='" . $last . "',								
										v_id='" . $_REQUEST['color_' . $i] . "',
										v_type='1'");
			$col_arary[] = $_REQUEST['color_' . $i];
		}
	}
	$color_filter = implode(",", $col_arary);

	$size_num = $_REQUEST['size_num'] - 1;
	$size_arary = array();
	for ($i = 1; $i <= $size_num; $i++) {
		if ($_REQUEST['size_' . $i] != "") {
			$qry_detail = dbQuery("insert into tabl_product_options set 										
										p_id='" . $last . "',								
										v_id='" . $_REQUEST['size_' . $i] . "',
										v_type='2',
										price='" . $_REQUEST['price_' . $i] . "'");
			$size_arary[] = $_REQUEST['size_' . $i];
		}
	}
	$size_filter = implode(",", $size_arary);

	dbQuery("UPDATE tabl_product_filter SET color='" . $color_filter . "',size='" . $size_filter . "' WHERE p_id='" . $last . "'");


	echo '<script>alert("Product Added!");window.location.href="products.php"</script>';
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
	<title><?php echo SITE; ?> - Add Products </title>
	<link rel="icon" type="image/x-icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
	<link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
	<script src="assets/js/loader.js"></script>

	<!-- BEGIN GLOBAL MANDATORY STYLES -->
	<link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
	<link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
	<link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
	<!-- END GLOBAL MANDATORY STYLES -->

	<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
	<link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
	<link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
	<!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

	<link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
	<link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

	<link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
	<link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

</head>

<body class="alt-menu sidebar-noneoverflow">

	<?php include('inc/__header.php'); ?>
	<!--  BEGIN MAIN CONTAINER  -->
	<div class="main-container" id="container">

		<div class="overlay"></div>
		<div class="search-overlay"></div>

		<!--  BEGIN TOPBAR  -->
		<?php include('inc/__menu.php'); ?>
		<!--  END TOPBAR  -->

		<!--  BEGIN CONTENT PART  -->
		<div id="content" class="main-content">
			<div class="layout-px-spacing">
				<nav class="breadcrumb-one" aria-label="breadcrumb">
					<ol class="breadcrumb">
						<li class="breadcrumb-item"><a href="index.php">Home</a></li>
						<li class="breadcrumb-item"><a href="products.php">Products</a></li>
						<li class="breadcrumb-item active"><a href="javascript:void(0);">Add</a></li>

					</ol>
				</nav>
				<div class="account-settings-container layout-top-spacing">

					<div class="account-content">
						<div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
							<div class="row">
								<div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
									<form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">

										<div class="info">
											<h6 class="">Add Product</h6>
											<div class="row">
												<div class="col-lg-11 mx-auto">
													<div class="row">
														<div class="col-xl-12 col-lg-12 col-md-8 mt-md-0 mt-4">
															<div class="form">
																<div class="form-row">
																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Name</strong></label>
																		<input type="text" class="form-control" id="Name" name="name" placeholder="Name" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Product Code</strong></label>
																		<input type="text" class="form-control" id="product_code" name="product_code" placeholder="Product Code" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Qty</strong></label>
																		<input type="text" class="form-control" id="qty" name="qty" placeholder="Quantity" onkeypress="return isDecimal(event,this)" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Main-Category</strong></label>
																		<select name="main_cat_id" class="form-control" onChange="set_cat(this.value)" required>
																			<option value="">SELECT</option>
																			<?php $main_cat = dbQuery("SELECT * FROM tabl_main_category ORDER BY name ASC");
																			while ($res_main_cat = dbFetchAssoc($main_cat)) { ?>
																				<option value="<?php echo $res_main_cat['id']; ?>"><?php echo $res_main_cat['name']; ?></option>
																			<?php } ?>
																		</select>
																	</div>

																	<!-- <div class="form-group col-md-4">
																		<label for="fullName"><strong>Category</strong></label>
																		<select name="cat_id" id="cat_id" class="form-control" onChange="set_sub_cat(this.value)" required>

																		</select>
																	</div>

																	<div class="form-group col-md-4">
																		<label for="fullName"><strong>Sub-Category</strong></label>
																		<select name="sub_cat_id" id="sub_cat_id" class="form-control" required>

																		</select>
																	</div> -->


																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>MRP Price</strong></label>
																		<input type="text" class="form-control" id="Name" name="old_price" placeholder="MRP Price" onkeypress="return isDecimal(event,this)" required>
																	</div>
																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Offer Price</strong></label>
																		<input type="text" class="form-control" id="Name" name="price" placeholder="Offer Price" onkeypress="return isDecimal(event,this)" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Delivery Price</strong></label>
																		<input type="text" class="form-control" id="shipping_price" name="shipping_price" placeholder="Delivery Price" onkeypress="return isDecimal(event,this)" required>
																	</div>
																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Delivered</strong></label>
																		<input type="text" class="form-control" id="delivered" name="delivered" placeholder="Exp: 2-5 Business Days" required>
																	</div>

																</div>

																<div class="row">
																	<div class="col-sm-6">
																		<div class="form-group">
																			<label for="fullName"><strong>Short Description</strong></label>
																			<textarea class="form-control" id="short_description" name="short_description" placeholder="Short Description" required rows="6"></textarea>
																		</div>

																	</div>

																	<div class="col-sm-6">
																		<div class="form-group">
																			<label for="fullName"><strong>Full Description</strong></label>
																			<textarea class="form-control" id="description" name="description" placeholder="Description" required rows="6"></textarea>
																		</div>

																	</div>

																	<div class="col-sm-12">
																		<div class="form-group">
																			<label for="fullName"><strong>Details</strong></label>
																			<textarea class="form-control" id="details" name="details" placeholder="Details" required rows="6"></textarea>
																		</div>

																	</div>
																	<div class="col-sm-12">
																		<div class="form-group">
																			<label for="fullName">Cover Image</label>
																			<input type="file" class="form-control mb-4" id="Name" name="image" placeholder="Image" required>
																		</div>
																	</div>

																	<div class="col-xl-12 col-md-12 col-sm-12 col-12">
																		<h5 style="color:#d06a6a">Add More Images</h5>
																	</div>
																	<table class="table table-bordered table-striped">
																		<tr>
																			<th>
																				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;">
																					<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
																					<line x1="12" y1="8" x2="12" y2="16"></line>
																					<line x1="8" y1="12" x2="16" y2="12"></line>
																				</svg>
																			</th>
																			<th>S.No.</th>
																			<th>Images</th>
																		</tr>
																		<tbody id="classes">
																		</tbody>
																	</table>


																	<!-- <div class="col-xl-12 col-md-12 col-sm-12 col-12">
		<h5 style="color:#d06a6a">Add Colors</h5>
	</div>
		<table class="table table-bordered table-striped">
		    <tr>
			    <th>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayColor()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
            </th>
			   <th>S.No.</th>
			   <th>Colors</th>
			</tr>
		 <tbody id="colors">
          </tbody>
		</table>		 -->


																	<!-- <div class="col-xl-12 col-md-12 col-sm-12 col-12">
																		<h5 style="color:#d06a6a">Add Quantity</h5>
																	</div>
																	<table class="table table-bordered table-striped">
																		<tr>
																			<th>
																				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displaySize()" name="add" style="cursor:pointer;">
																					<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
																					<line x1="12" y1="8" x2="12" y2="16"></line>
																					<line x1="8" y1="12" x2="16" y2="12"></line>
																				</svg>
																			</th>
																			<th>S.No.</th>
																			<th>Quantity</th>
																			<th>Price</th>
																		</tr>
																		<tbody id="sizes">
																		</tbody>
																	</table> -->



																	<div class="col-sm-12">
																		<div class="form-group">
																			<label for="fullName"></label>
																			<input type="hidden" name="num" id="num" value="1" />
																			<input type="hidden" name="color_num" id="color_num" value="1" />
																			<input type="hidden" name="size_num" id="size_num" value="1" />

																			<button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Submit</button>
																		</div>

																	</div>



																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>

									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
				<?php include('inc/__footer.php'); ?>
			</div>

		</div>
		<!--  END CONTENT PART  -->

	</div>
	<!-- END MAIN CONTAINER -->

	<!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
	<script src="assets/js/libs/jquery-3.1.1.min.js"></script>
	<script src="bootstrap/js/popper.min.js"></script>
	<script src="bootstrap/js/bootstrap.min.js"></script>
	<script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
	<script src="assets/js/app.js"></script>
	<script>
		$(document).ready(function() {
			App.init();
		});
	</script>
	<script src="assets/js/custom.js"></script>
	<!-- END GLOBAL MANDATORY SCRIPTS -->

	<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
	<script src="plugins/apex/apexcharts.min.js"></script>
	<script src="assets/js/dashboard/dash_2.js"></script>
	<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

	<script src="plugins/dropify/dropify.min.js"></script>
	<script src="plugins/blockui/jquery.blockUI.min.js"></script>
	<!-- <script src="plugins/tagInput/tags-input.js"></script> -->
	<script src="assets/js/users/account-settings.js"></script>

	<script>
		function displayResult()

		{

			var i = document.getElementById("num").value;

			document.getElementById("classes").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><input type="file" name="images_' + i + '" id="images_' + i + '"  class="form-control" accept="image/*"></td></tr>';
			i++;

			var num = document.getElementById("num").value = i;

		}
	</script>


	<script>
		function displayColor()

		{

			var i = document.getElementById("color_num").value;

			document.getElementById("colors").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayColor()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><select name="color_' + i + '" id="color_' + i + '" class="form-control"><?php echo get_all_color(); ?></select></td></tr>';
			i++;

			var color_num = document.getElementById("color_num").value = i;

		}
	</script>


	<script>
		function displaySize()

		{

			var i = document.getElementById("size_num").value;

			document.getElementById("sizes").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displaySize()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><select name="size_' + i + '" id="size_' + i + '" class="form-control"><?php echo get_all_size(); ?></select></td><td><input type="text" name="price_' + i + '" id="price_' + i + '" class="form-control"  onkeypress="return isDecimal(event,this)"></td></tr>';
			i++;

			var color_num = document.getElementById("size_num").value = i;

		}
	</script>




	<script>
		function isNumber(evt) {
			var iKeyCode = (evt.which) ? evt.which : evt.keyCode
			if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
				return false;

			return true;
		}

		function isDecimal(evt, obj) {

			var charCode = (evt.which) ? evt.which : event.keyCode
			var value = obj.value;
			var dotcontains = value.indexOf(".") != -1;
			if (dotcontains)
				if (charCode == 46) return false;
			if (charCode == 46) return true;
			if (charCode > 31 && (charCode < 48 || charCode > 57))
				return false;
			return true;
		}
	</script>
	<script>
		function set_cat(cat_id) {
			if (cat_id != "") {
				$.ajax({
					url: 'ajax/get_category.php',
					type: 'post',
					data: {
						'cat_id': cat_id
					},
					success: function(data) {
						if (data != "") {
							$("#cat_id").html(data);
						}
					},
				});
			} else {
				$("#cat_id").html('<option value="">SELECT</option>');
				$("#sub_cat_id").html('<option value="">SELECT</option>');
			}
		}
	</script>
	<script>
		function set_sub_cat(cat_id) {
			if (cat_id != "") {
				$.ajax({
					url: 'ajax/get_sub_category.php',
					type: 'post',
					data: {
						'cat_id': cat_id
					},
					success: function(data) {
						if (data != "") {
							$("#sub_cat_id").html(data);
						}
					},
				});
			} else {
				$("#sub_cat_id").html('<option value="">SELECT</option>');
			}
		}
	</script>
	<script>
		CKEDITOR.replace('description');
	</script>
	<script>
		CKEDITOR.replace('short_description');
	</script>
	<script>
		CKEDITOR.replace('details');
	</script>

	<script>
		function isNumber(evt) {
			var iKeyCode = (evt.which) ? evt.which : evt.keyCode
			if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
				return false;

			return true;
		}

		function isDecimal(evt, obj) {

			var charCode = (evt.which) ? evt.which : event.keyCode
			var value = obj.value;
			var dotcontains = value.indexOf(".") != -1;
			if (dotcontains)
				if (charCode == 46) return false;
			if (charCode == 46) return true;
			if (charCode > 31 && (charCode < 48 || charCode > 57))
				return false;
			return true;
		}
	</script>

</body>

</html>