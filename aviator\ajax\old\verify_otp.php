<?php
session_start();
include('../admin/lib/db_connection.php');
include('../admin/lib/get_functions.php');
include('../admin/inc/resize-class.php');
$page = 2;
$sub_page = 22;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if ($_REQUEST['action'] == 'submit') {

    $otp    =   mysqli_real_escape_string($con, $_REQUEST['otp']);
    $cotp   =   $_SESSION["otp"];

    // $fname          =   $_SESSION["fname"];
    // $lname          =   $_SESSION["lname"];
    // $name          =   $_SESSION["name"];
    $email          =   $_SESSION["email"];
    $phone          =   $_SESSION["phone"];
    $password       =   $_SESSION["password"];
    $password       =   $_SESSION["ref_code"];
    $status         =   1;


    if ($otp == $cotp) {

        $amount = 0;
        $amount = 0;

        $result = dbQuery("INSERT INTO tabl_user SET `email`='" . $email . "',phone='" . $phone . "',ref_code='" . $ref_code . "',password='" . $password . "',status='" . $status . "', date='" . $date . "'");
        $user_id = dbInsertId();


        // $result2 = dbQuery("INSERT INTO tabl_wallet SET user_id='" . $user_id . "',amount='" . $amount . "',amount='" . $amount . "',status='" . $status . "', date='" . $date . "'");


        // $q1 = "INSERT INTO user (name, email_id, mobile_no, gender, city, password, repassword, token) values('$fname', '$lname', '$email', '$phone', '$gender', '$city', '$pass', '$cpass', '$token')";
        // $status = mysqli_query($con, $q1);
        // mysqli_close($con);


        if ($result) {
            echo 1;
        } else {
            echo 2;
        }
    } else {
        echo 0;
    }

    //echo "INSERT INTO tabl_user SET name='" . $name . "',email='" . $email . "',phone='" . $phone . "',password='" . $password . "',status='" . $status . "', date='".$date."'";

}
