<?php
session_start();
include('../admin/lib/db_connection.php');
include('../admin/lib/get_functions.php');
include('../admin/inc/resize-class.php');
$page = 2;
$sub_page = 22;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if ($_REQUEST['action'] == 'submit') {

    // $fname           =   mysqli_real_escape_string($con, $_REQUEST['fname']);
    // $lname           =   mysqli_real_escape_string($con, $_REQUEST['lname']);
    // $name           =   mysqli_real_escape_string($con, $_REQUEST['name']);
    $email          =   mysqli_real_escape_string($con, $_REQUEST['email']);
    $phone          =   mysqli_real_escape_string($con, $_REQUEST['phone']);
    $password       =   mysqli_real_escape_string($con, $_POST['password']);
    $repassword     =   mysqli_real_escape_string($con, $_POST['repassword']);
    $ref_code     =   mysqli_real_escape_string($con, $_POST['ref_code']);
    $status = 1;
    //default.png


    if ($password == $repassword) {

        $pass = password_hash($password, PASSWORD_BCRYPT);
        $cpass = password_hash($repassword, PASSWORD_BCRYPT);

        // $otp = mt_rand(100000, 999999);
        $otp = 1234;

        //$result = dbQuery("INSERT INTO tabl_user SET name='" . $name . "',email='" . $email . "',password='" . $pass . "',status='" . $status . "'");

        // $to          = $email; //'<EMAIL>';
        // $subject     = "PM kishanmart Registration";
        // $message     = "Thank you for joining us, We hope that you will happy with be our service.";
        // $message2    = "Your OTP is " . $otp;


        // include("../mail.php");
        // sendmail($to, $subject, $message, $message2, $otp, $name)

        // if (sendmail($to, $subject, $message, $message2, $name)) {

        //     $_SESSION["otp"] = $otp;
        //     // $_SESSION["fname"] = $fname;
        //     // $_SESSION["lname"] = $lname;
        //     $_SESSION["name"]       = $name;
        //     $_SESSION["phone"]      = $phone;
        //     $_SESSION["email"]      = $email;
        //     $_SESSION["password"]   = $pass;
            
        //     echo 1;
        // } else {
        //     echo 0;
        // }

        // $_SESSION["otp"] = $otp;
        // $_SESSION["name"]       = $name;
        // $_SESSION["phone"]      = $phone;
        // $_SESSION["email"]      = $email;
        // $_SESSION["password"]   = $pass;
        // echo 1;


        $result = dbQuery("INSERT INTO tabl_user SET `email`='" . $email . "',ref_code='" . $ref_code . "',phone='" . $phone . "',password='" . $password . "',status='" . $status . "', date='" . $date . "'");
        $user_id = dbInsertId();

        if ($result) {
            $own_code = rand(100000, 999999);
            $result = dbQuery("UPDATE tabl_user SET own_code = '" . $own_code . "' WHERE `id` = '" . $user_id . "'");

            echo 1;
        } else {
            echo 0;
        }
    } else {
        echo 3;
    }
}
