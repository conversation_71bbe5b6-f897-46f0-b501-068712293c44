<?php

session_start();

include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');

include('inc/resize-class.php');


$page = 1;
$sub_page = 0;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- DashBoard</title>
  <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->

  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />

  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />

  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include('inc/__header.php'); ?>

  <!--  BEGIN MAIN CONTAINER  -->

  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->

    <?php include('inc/__menu.php'); ?>

    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->

    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <div class="row layout-top-spacing">
          
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing">
            <div class="widget-four">

              <div class="widget-heading mb-0">
                <h5 class="">Recent General Enqueries</h5>
                <div class="table-responsive mb-0 mt-2">
                  <div class="table-responsive mb-0 mt-2">
                    <table id="zero-config" class="table table-hover" style="width:100%">
                      <thead>
                        <tr>
                          <th>S No.</th>
                          <th>Date</th>
                          <th>Name</th>
                          <th>Email</th>
                          <th>Message</th>
                          <th>Action</th>

                        </tr>
                      </thead>
                      <tbody style="text-transform: capitalize">
                        <?php
                        $sel = dbQuery("SELECT * FROM tabl_contactus WHERE status='1' ORDER BY date DESC LIMIT 10");
                        $i = 1;
                        while ($res = dbFetchAssoc($sel)) {


                        ?>
                          <tr>
                            <td><?php echo $i; ?></td>
                            <td>
                              <?php

                              $date = strtotime($res['date']);

                              echo date("d M Y", $date);

                              ?>
                            </td>
                            <td><?php echo $res['name']; ?></td>
                            <td><?php echo $res['email']; ?></td>
                            <td><?php echo $res['msg']; ?></td>
                            <td>
                              <a href="javascript:void(0)" onClick="delete_entry('tabl_contactus',0,<?php echo $res['id'] ?>)">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2">
                                  <polyline points="3 6 5 6 21 6"></polyline>
                                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                  <line x1="10" y1="11" x2="10" y2="17"></line>
                                  <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                              </a>
                            </td>


                          </tr>
                        <?php $i++;
                        } ?>
                      </tbody>
                    </table>
                  </div>
                </div>


              </div>


            </div>
          </div>

        </div>
        <?php include('inc/__footer.php'); ?>
      </div>
    </div>

    <!--  END CONTENT PART  -->

  </div>

  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->

  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function() {


      App.init();


    });
  </script>


  <script src="assets/js/custom.js"></script>

  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

</body>

</html>

<script>
  function delete_entry(tabl, val, row_id) {
    var retVal = confirm("Are you sure want to delete this enrty.");
    if (retVal == true) {
      $.ajax({
        url: 'ajax/activate.php',
        type: 'post',
        data: {
          'tabl': tabl,
          'val': val,
          'row_id': row_id
        },
        success: function(data) {
          //alert(data);
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }


  }


  function return_order_success(order_id, row_id) {
    var retVal = confirm("Are you sure want to Return this order.");
    if (retVal == true) {
      $(".return_order_" + row_id).html('<img src="../loader.gif" style="width: 10px;"></i> please wait...');
      $.ajax({
        url: 'ajax/return_order_success.php',
        type: 'post',
        data: {
          'order_id': order_id
        },
        success: function(data) {
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }
  }
</script>