<?php
session_start();
include ('../admin/lib/db_connection.php');
include ('../admin/lib/get_functions.php');
include ('../admin/inc/resize-class.php');
$page = 2;
$sub_page = 22;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if ($_REQUEST['action'] == 'submit') {

    // $fname           =   mysqli_real_escape_string($con, $_REQUEST['fname']);
    // $lname           =   mysqli_real_escape_string($con, $_REQUEST['lname']);

    $name = mysqli_real_escape_string($con, $_REQUEST['name']);
    $email = mysqli_real_escape_string($con, $_REQUEST['email']);
    $phone = mysqli_real_escape_string($con, $_REQUEST['phone']);
    $password = mysqli_real_escape_string($con, $_POST['password']);
    $repassword = mysqli_real_escape_string($con, $_POST['repassword']);
    $ref_code = mysqli_real_escape_string($con, $_POST['ref_code']);
    $otp = mysqli_real_escape_string($con, $_POST['otp']);

    if ($ref_code == '') {
        $ref_code = '00000000';
    }
    if ($otp!=$_SESSION['otp']) {
       echo 17;
       exit();
    }

    $status = 1;
    //default.png


    $sel_user = dbQuery("SELECT * FROM tabl_user WHERE own_code='$ref_code'");
    if ($res_user = dbFetchAssoc($sel_user) || $ref_code == '00000000') {


        $sel_user_2 = dbQuery("SELECT * FROM tabl_user WHERE email='$email' OR phone='$phone'");
        if (!$res_user_2 = dbFetchAssoc($sel_user_2)) {

            if ($password == $repassword) {

                $pass = password_hash($password, PASSWORD_BCRYPT);
                $cpass = password_hash($repassword, PASSWORD_BCRYPT);

                // $otp = mt_rand(100000, 999999);
                $otp = 1234;

                //$result = dbQuery("INSERT INTO tabl_user SET name='" . $name . "',email='" . $email . "',password='" . $pass . "',status='" . $status . "'");

                // $to          = $email; //'<EMAIL>';
                // $subject     = "PM kishanmart Registration";
                // $message     = "Thank you for joining us, We hope that you will happy with be our service.";
                // $message2    = "Your OTP is " . $otp;


                // include("../mail.php");
                // sendmail($to, $subject, $message, $message2, $otp, $name)

                // if (sendmail($to, $subject, $message, $message2, $name)) {

                //     $_SESSION["otp"] = $otp;
                //     // $_SESSION["fname"] = $fname;
                //     // $_SESSION["lname"] = $lname;
                //     $_SESSION["name"]       = $name;
                //     $_SESSION["phone"]      = $phone;
                //     $_SESSION["email"]      = $email;
                //     $_SESSION["password"]   = $pass;

                //     echo 1;
                // } else {
                //     echo 0;
                // }

                // $_SESSION["otp"] = $otp;
                // $_SESSION["name"]       = $name;
                // $_SESSION["phone"]      = $phone;
                // $_SESSION["email"]      = $email;
                // $_SESSION["password"]   = $pass;
                // echo 1;


                // $result = dbQuery("INSERT INTO tabl_user SET `name`='" . $name . "', `email`='" . $email . "',ref_code='" . $ref_code . "',phone='" . $phone . "',password='" . $password . "',status='" . $status . "', date='" . $date . "'");

                $result = dbQuery("INSERT INTO tabl_user SET `name`='" . $name . "', `email`='" . $email . "',ref_code='" . $ref_code . "',phone='" . $phone . "',password='" . $pass . "',status='" . $status . "', date='" . $date . "'");
                $user_id = dbInsertId();

                if ($result) {
                    $own_code = 'H' . rand(100000, 999999);
                    $result = dbQuery("UPDATE tabl_user SET own_code = '" . $own_code . "' WHERE `id` = '" . $user_id . "'");
                    $result = dbQuery("INSERT INTO tabl_wallet SET `user_id`='" . $user_id . "', `amount`='0', envelopestatus='0', date='" . $date . "'");


                    $sel_level = dbQuery("SELECT * FROM tabl_level ORDER BY `level` ASC");
                    if ($res_level = dbFetchAssoc($sel_level)) {
                        // $result = dbQuery("INSERT INTO tabl_my_level SET `user_id`='" . $user_id . "', vip_level='" . $res_level['level'] . "', date='" . $date . "'");
                        upgrade_level($user_id, $res_level['level']);

                        $self_bonus = $res_setting['self_bonus'];
                        $invite_bonus = $res_setting['invite_bonus'];

                        if ($self_bonus > 0) {
                            $sql1 = dbQuery("INSERT INTO `tabl_walletsummery`(`user_id`,`order_id`,`amount`,`game`,`type`,`actiontype`, `date`) VALUES ('" . $user_id . "','0','" . $self_bonus . "','','credit','self_bonus', '$date')");
                            if ($sql1) {
                                update_wallet($user_id, $self_bonus, 'credit');
                            }
                        }

                        // $sel_user

                        $sel_user3 = dbQuery("SELECT * FROM tabl_user WHERE own_code='$ref_code'");
                        if ($res_user3 = dbFetchAssoc($sel_user3)) {
                            // if (isset($res_user3) && !empty($res_user3['id'])) {

                            if ($invite_bonus > 0) {
                                $upliner_id = $res_user3['id'];
                                $sql2 = dbQuery("INSERT INTO `tabl_walletsummery`(`user_id`,`order_id`,`amount`,`game`,`type`,`actiontype`, `date`) VALUES ('" . $upliner_id . "','" . $user_id . "','" . $invite_bonus . "','','credit','invitation_bonus', '$date')");
                                if ($sql2) {
                                    update_wallet($upliner_id, $invite_bonus, 'credit');
                                }
                            }
                        }

                        // $sql3 = dbQuery("INSERT INTO `tabl_walletsummery`(`user_id`,`order_id`,`amount`,`game`,`type`,`actiontype`) VALUES ('" . $user_id . "','" . $order_id . "','" . $invite_bonus . "','','credit','invite_bonus')");
                    }

                    echo 1;
                } else {
                    echo 0;
                }
            } else {
                echo 2;
            }
        } else {
            echo 3;
        }
    } else {
        echo 4;
    }
}
