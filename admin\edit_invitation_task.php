<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 6;
$sub_page = 61;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

if (isset($_REQUEST['submit'])) {
    $step_no = $_REQUEST['step_no'];
    $no_of_invites = $_REQUEST['no_of_invites'];
    $recharge_per_invite = $_REQUEST['recharge_per_invite'];
    $bonus = $_REQUEST['bonus'];

    $status = dbQuery("UPDATE tabl_invitation_task SET 
       `step_no`='" . $step_no . "',
        `no_of_invites`='" . $no_of_invites . "',
        `recharge_per_invite`='" . $recharge_per_invite . "',
        `bonus`='" . $bonus . "'
        WHERE  id='" . $_REQUEST['id'] . "'");

    if ($status) {
        echo '<script>alert("Invitation Tasks Updated!");window.location.href="invitation_task.php"</script>';
    } else {
        echo '<script>alert("Something went wrong!");window.location.href="invitation_task.php"</script>';
    }
}

$sel = dbQuery("SELECT * FROM tabl_invitation_task WHERE id='" . $_REQUEST['id'] . "'");
$res = dbFetchAssoc($sel);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Edit Invitation Tasks
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="genre.php">Invitation Tasks</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Edit</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info"
                                        enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Edit Invitation Tasks</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">

                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <label for="step_no">Step No</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="step_no" name="step_no"
                                                                                placeholder="Step No"
                                                                                value="<?php echo $res['step_no']; ?>"
                                                                                required>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <label for="no_of_invites">No of
                                                                                Invites</label>

                                                                            <input type="text" class="form-control mb-4"
                                                                                id="no_of_invites" name="no_of_invites"
                                                                                placeholder="No of Invites"
                                                                                value="<?php echo $res['no_of_invites']; ?>"
                                                                                required
                                                                                onkeypress="return isNumber(event);">
                                                                        </div>
                                                                    </div>


                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <label for="recharge_per_invite">Recharge
                                                                                per Invite</label>

                                                                            <input type="text" class="form-control mb-4"
                                                                                id="recharge_per_invite"
                                                                                name="recharge_per_invite"
                                                                                placeholder="Recharge per Invite"
                                                                                value="<?php echo $res['recharge_per_invite']; ?>"
                                                                                required
                                                                                onkeypress="return isDecimal(event, this);">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <label for="bonus">Bonus</label>

                                                                            <input type="text" class="form-control mb-4"
                                                                                id="bonus" name="bonus"
                                                                                placeholder="Bonus"
                                                                                value="<?php echo $res['bonus']; ?>"
                                                                                required
                                                                                onkeypress="return isNumber(event);">
                                                                        </div>
                                                                    </div>



                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit"
                                                                                class="btn btn-secondary mb-4 mr-2">Submit</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
    CKEDITOR.replace('description');
</script>