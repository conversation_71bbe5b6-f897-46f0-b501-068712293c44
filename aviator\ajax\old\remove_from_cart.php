<?php
session_start();
include('../admin/lib/db_connection.php');
$date = date('Y-m-d H:i:s');

//echo $num_rows;

if (isset($_REQUEST['cart_id'])) {

    $user_id = $_SESSION['user_id'];
    $cart_id = $_REQUEST['cart_id'];

    $sel = dbQuery("SELECT * FROM tabl_cart WHERE id='" . $cart_id . "' ");
    $res = dbFetchAssoc($sel);

    $result = dbQuery("DELETE FROM tabl_cart WHERE id='" . $cart_id . "'");

    if ($result) {

        $sel_cart = dbQuery("SELECT SUM(total) as cart_total FROM tabl_cart WHERE user_id='" . $user_id . "' ");
        $res_cart = dbFetchAssoc($sel_cart);

        $cart_total = $res_cart['cart_total'];

        $shipping = 120;
        $tax = $cart_total * (5 / 100);
        $sub_total = $cart_total - $tax;
        $grand_total = $sub_total + $tax + $shipping;

        echo 1 . '~' . $sub_total . '~' . $tax . '~' . $shipping . '~' . $grand_total;
    } else {
        echo 0;
    }
} else {
    echo '0';
}
