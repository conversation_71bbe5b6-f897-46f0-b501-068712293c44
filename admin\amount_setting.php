<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 101;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['submit'])) {
    // $upd = dbQuery("UPDATE tabl_setting SET min_recharge='" . $_REQUEST['min_recharge'] . "', min_withdraw='" . $_REQUEST['min_withdraw'] . "', usdt_to_inr_rate='" . $_REQUEST['usdt_to_inr_rate'] . "', invite_bonus='" . $_REQUEST['invite_bonus'] . "',level_1_comm='" . $_REQUEST['level_1_comm'] . "',level_2_comm='" . $_REQUEST['level_2_comm'] . "',level_3_comm='" . $_REQUEST['level_3_comm'] . "' WHERE id='1'");

    $upd = dbQuery("UPDATE tabl_setting SET min_recharge='" . $_REQUEST['min_recharge'] . "', min_withdraw='" . $_REQUEST['min_withdraw'] . "', usdt_to_inr_rate='" . $_REQUEST['usdt_to_inr_rate'] . "', invite_bonus='" . $_REQUEST['invite_bonus'] . "', self_bonus='" . $_REQUEST['self_bonus'] . "' WHERE id='1'");
    echo '<script>alert("Amount Setting Updated!");window.location.href="amount_setting.php"</script>';
}

$sel = dbQuery("SELECT * FROM tabl_setting WHERE id='1'");
$res = dbFetchAssoc($sel);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Setting
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />

</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Setting</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info"
                                        enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Setting</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">

                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="invite_bonus">
                                                                                Invite Bonus Amount
                                                                            </label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="invite_bonus" name="invite_bonus"
                                                                                placeholder="invite_bonus"
                                                                                value="<?php echo $res['invite_bonus']; ?>">
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="self_bonus">
                                                                                Self Bonus
                                                                            </label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="self_bonus" name="self_bonus"
                                                                                placeholder="self_bonus"
                                                                                value="<?php echo $res['self_bonus']; ?>">
                                                                        </div>
                                                                    </div>


                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="min_recharge">Min
                                                                                Recharge</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="min_recharge" name="min_recharge"
                                                                                placeholder="min_recharge"
                                                                                value="<?php echo $res['min_recharge']; ?>">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="min_withdraw">Min
                                                                                Withdraw</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="min_withdraw" name="min_withdraw"
                                                                                placeholder="min_withdraw"
                                                                                value="<?php echo $res['min_withdraw']; ?>">
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="usdt_to_inr_rate">USDT to INR
                                                                                Rate</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="usdt_to_inr_rate"
                                                                                name="usdt_to_inr_rate"
                                                                                placeholder="usdt_to_inr_rate"
                                                                                value="<?php echo $res['usdt_to_inr_rate']; ?>">
                                                                        </div>
                                                                    </div>




                                                                    <!-- <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="invite_bonus">Invite Bonus
                                                                                Amount</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="invite_bonus" name="invite_bonus"
                                                                                placeholder="invite_bonus"
                                                                                value="<?php echo $res['invite_bonus']; ?>">
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="level_1_comm">Level 1 Commission
                                                                                Percent [in %]</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="level_1_comm" name="level_1_comm"
                                                                                placeholder="level_1_comm"
                                                                                value="<?php echo $res['level_1_comm']; ?>">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="level_2_comm">Level 2 Commission
                                                                                Percent [in %]</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="level_2_comm" name="level_2_comm"
                                                                                placeholder="level_2_comm"
                                                                                value="<?php echo $res['level_2_comm']; ?>">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="level_3_comm">Level 3 Commission
                                                                                Percent [in %]</label>
                                                                            <input type="text" class="form-control mb-4"
                                                                                id="level_3_comm" name="level_3_comm"
                                                                                placeholder="level_3_comm"
                                                                                value="<?php echo $res['level_3_comm']; ?>">
                                                                        </div>
                                                                    </div> -->


                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit"
                                                                                class="btn btn-secondary mb-4 mr-2">Submit</button>
                                                                        </div>

                                                                    </div>



                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>