<?php
include ("../admin/lib/db_connection.php");

$game_type = $_POST['game_type'];

$today = date('Y-m-d');

// Number of items per page
$itemsPerPage = 10;

// Current page
$currentPage = isset($_POST['page']) ? $_POST['page'] : 1;
$offset = ($currentPage - 1) * $itemsPerPage;

// Database query with pagination
$query = "SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' AND DATE(`date`)='$today' ORDER BY id DESC LIMIT $offset, $itemsPerPage";
$parityrecordQuery = dbQuery($query);

// Total number of rows
$totalRows = dbNumRows(dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' AND DATE(`date`)='$today'"));

// Calculate total number of pages
$totalPages = ceil($totalRows / $itemsPerPage);
?>

<div class="table-container">
    <!-- Display data table -->
    <table class="table">
        <!-- Table header -->
        <thead>
            <tr>
                <!-- Table headers -->
                <th style="background-color: #0a58ca; color: #fff;">Period</th>
                <th style="background-color: #0a58ca; color: #fff;">Number</th>
                <th style="background-color: #0a58ca; color: #fff;">Big Small</th>
                <th style="background-color: #0a58ca; color: #fff;">Color</th>
            </tr>
        </thead>
        <!-- Table body -->
        <tbody>
            <!-- Loop through fetched rows -->
            <?php
            while ($parityResult = dbFetchAssoc($parityrecordQuery)) {
                // Display table rows
                // (Your existing code here)
            

                $result = $parityResult['result'];
                $color = $parityResult['color'];

                ?>
                <tr>
                    <!-- <td><?php echo substr($parityResult['game_id'], 8); ?></td> -->
                    <td>
                        <?php echo $parityResult['game_id']; ?>
                    </td>
                    <td>
                        <?php echo $parityResult['result']; ?>
                    </td>
                    <td>
                        <?php echo $parityResult['num_type']; ?>
                    </td>
                    <td>
                        <?php
                        if ($color == 'orange') {

                            ?>
                            <i class="fa-solid fa-circle"
                                style="color: rgb(255, 123, 0);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                            </i>
                            <?php
                        } else if ($color == 'green') {
                            ?>
                                <i class="fa-solid fa-circle"
                                    style="color: green;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                </i>
                            <?php
                        } else if ($color == 'orange+white') {
                            ?>
                                    <i class="fa-solid fa-circle"
                                        style="color: rgb(255, 123, 0);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                    </i>
                                    <i class="fa-solid fa-circle"
                                        style="color: white;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                    </i>
                            <?php
                        } else if ($color == 'green+white') {
                            ?>
                                        <i class="fa-solid fa-circle"
                                            style="color: green;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                        </i>
                                        <i class="fa-solid fa-circle"
                                            style="color: white;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                        </i>
                            <?php
                        }
                        ?>

                        <!-- <i class="fa-solid fa-circle"
                        style="color: rgb(255, 255, 255);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                    </i> -->
                    </td>
                </tr>
                <?php
            }
            ?>
        </tbody>
    </table>
</div>

<!-- Pagination controls -->
<div class="pagination">
    <?php if ($currentPage > 1): ?>
        <a href="?page=<?php echo $currentPage - 1; ?>">Previous</a>
    <?php endif; ?>

    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
        <a href="?page=<?php echo $i; ?>" <?php echo ($i == $currentPage) ? 'class="active"' : ''; ?>><?php echo $i; ?></a>
    <?php endfor; ?>

    <?php if ($currentPage < $totalPages): ?>
        <a href="?page=<?php echo $currentPage + 1; ?>">Next</a>
    <?php endif; ?>
</div>

<div class="pagination">
    <button class="page_btn">
        <svg xmlns="http://www.w3.org/2000/svg" class="btn--icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
        </svg>
    </button>
    <div class="pages">
        <a class="page">1</a>
        <a class="page">2</a>
        <a class="page active">3</a>
        <a class="page">4</a>
        <a class="page">5</a>
        <a class="page">6</a>
        <a class="page">...</a>
        <a class="page">23</a>
    </div>
    <button class="page_btn">
        <svg xmlns="http://www.w3.org/2000/svg" class="btn--icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
        </svg>
    </button>
</div>


<?php

// echo $totalPages;
echo $totalRows;