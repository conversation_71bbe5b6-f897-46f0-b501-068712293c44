<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 101;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['withdrawal_id'])) {
    $id = $_REQUEST['withdrawal_id'];
} else {
    echo '<script>window.location.href="business_enquiry.php"</script>';
}

$sel = dbQuery("SELECT * FROM tabl_withdrawal WHERE id='" . $id . "'");
$res = dbFetchAssoc($sel);

$query = dbQuery("SELECT * FROM  `tabl_bankdetail` WHERE  id =  '" . $res['bankdetail_id'] . "' ");
$row = dbFetchAssoc($query);

// if ($row = dbFetchAssoc($query)) {
//     $state = $row['name'];
// } else {
//     $state = '';
// }


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Bank Details </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <style>
        .heading {
            font-size: 20px;
        }

        .data {
            font-size: 20px;
        }

        /* table,
        th,
        td {
            border: 1px solid black;
            border-collapse: collapse;
        } */

        th,
        td {
            padding: 5px;
            text-align: left;
            text-transform: capitalize
        }

        th {
            width: 30%
        }
    </style>
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Bank Details</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing ">
                                    <div class="info section general-info">

                                        <div class="row">
                                            <div class="col-lg-11 mx-auto mb-4">
                                                <h5 class="mt-4 mb-4">Bank Details</h5>
                                                <table style="width:100%">

                                                    <tr>
                                                        <th>Bank Name:</th>
                                                        <td><?php echo $row['bankname']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Account No:</th>
                                                        <td><?php echo $row['account']; ?></td>
                                                    </tr>

                                                    <tr>
                                                        <th>IFSC Code:</th>
                                                        <td><?php echo $row['ifsc']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Name:</th>
                                                        <td><?php echo $row['name']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Mobile:</th>
                                                        <td><?php echo $row['phone']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>UPI ID:</th>
                                                        <td><?php echo $row['upi_id']; ?></td>
                                                    </tr>

                                                    <tr>
                                                        <th>Date:</th>
                                                        <td>
                                                            <?php

                                                            $date = strtotime($res['date']);

                                                            echo date("d M Y", $date);

                                                            ?>
                                                        </td>
                                                    </tr>
                                                    <!-- <tr>
                                                        <th>Vendor Status:</th>
                                                        <td>
                                                            <?php
                                                            if ($res['status']) {
                                                                echo "Active";
                                                            } else {
                                                                echo "Inactive";
                                                            }

                                                            ?>
                                                        </td>
                                                    </tr> -->

                                                </table>

                                                <!-- <div class="row">
                                                    <div class="col-3">
                                                        <p class="heading">Company Address:</p>
                                                    </div>
                                                    <div class="col-5">
                                                        <p class="data"></p>
                                                    </div>
                                                    <div class="col-2">
                                                        <p class="heading">Company Name:</p>
                                                    </div>
                                                    <div class="col-4">
                                                        <p class="data"><?php echo $res['company']; ?></p>
                                                    </div>

                                                </div> -->
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>