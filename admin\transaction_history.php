<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 11;
$sub_page = 113;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');


if (isset($_REQUEST['user_id'])) {
    $user_id = $_REQUEST['user_id'];
}

$userq = dbQuery("SELECT * FROM tabl_user WHERE id='$user_id'");
$user = dbFetchAssoc($userq);



function numberAbbreviation($number)
{
    $abbreviations = array(
        "T" => 1000000000000,
        "B" => 1000000000,
        "M" => 1000000,
        "K" => 1000
    );

    foreach ($abbreviations as $abbreviation => $threshold) {
        if (abs($number) >= $threshold) {
            $formatted_number = number_format($number / $threshold, 1);
            return $formatted_number . $abbreviation;
        }
    }

    return $number;
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Transaction History
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <!-- <script src="assets/js/loader.js"></script> -->

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">

    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">


    <style>
        .red1 {
            color: #ff7374 !important;
            font-size: 14px !important;
            font-weight: 600 !important;
        }


        .green {
            color: green !important;
            font-size: 14px !important;
            font-weight: 600 !important;
        }
    </style>
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Transaction History</a></li>
                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

                                    <div class="widget-content widget-content-area br-6">
                                        <!-- <h4>Transaction History</h4> -->

                                        <div class="row">
                                            <div class="col-8">
                                                <h5>User: <?= $user['name']; ?></h5>
                                            </div>
                                            <div class="col-4 text-right">
                                                <h5>Wallet Balance: ₹ <?= check_wallet($user['id']); ?></h5>
                                            </div>
                                        </div>


                                        <div class="table-responsive mb-4 mt-4">
                                            <table id="zero-config" class="table table-hover" style="width:100%">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Date</th>
                                                        <th>Type</th>
                                                        <th>Details</th>

                                                        <th>Amount</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $s = 1;
                                                    $sel_wallet_summery = dbQuery("SELECT * FROM `tabl_walletsummery` WHERE user_id='$user_id' AND (actiontype!='transfer_to_safe' AND actiontype!='transfer_from_safe' AND actiontype!='daily_income') ORDER BY id DESC");

                                                    while ($res_wallet_summery = dbFetchAssoc($sel_wallet_summery)) {

                                                        $action_name = "";
                                                        $details = "";

                                                        $actiontypearray = explode("~", $res_wallet_summery['actiontype']);
                                                        @$actiontype = $actiontypearray[0];
                                                        @$actiontypeval = $actiontypearray[1];
                                                        if ($actiontype == 'recharge') {
                                                            $action_name = "Recharge";
                                                            $action_color = "green";

                                                            // $sel_deposits = dbQuery("SELECT * FROM tabl_deposits WHERE id='" . $res_wallet_summery['order_id'] . "'");
                                                            // if ($res_deposit = dbFetchAssoc($sel_deposits)) {
                                                            //     $details = "Tx No.: " . $res_deposit['ref_num'];
                                                            // }
                                                    
                                                            $sel_deposits = dbQuery("SELECT * FROM tabl_deposits WHERE id='" . $res_wallet_summery['order_id'] . "'");
                                                            if ($res_deposit = dbFetchAssoc($sel_deposits)) {
                                                                // $details = "Tx No.: " . $res_deposit['ref_num'];
                                                    
                                                                if ($res_deposit['status'] == 1) {
                                                                    $details = '<span class="text-success">Approved</span>';
                                                                } else if ($res_deposit['status'] == 2) {
                                                                    $details = '<span class="text-danger">Rejected</span>';
                                                                } else if ($res_deposit['status'] == 0) {
                                                                    $details = '<span class="text-warning">Pending</span>';
                                                                }
                                                            }

                                                        } else if ($actiontype == 'withdraw') {
                                                            $action_name = "Withdraw";
                                                            $action_color = "red1";

                                                            $sel_withdraw = dbQuery("SELECT * FROM tabl_withdrawal WHERE id='" . $actiontypeval . "'");
                                                            if ($res_withdraw = dbFetchAssoc($sel_withdraw)) {

                                                                if ($res_withdraw['status'] == 1) {
                                                                    $details = '<span class="text-success">Approved</span>';
                                                                } else if ($res_withdraw['status'] == 2) {
                                                                    $details = '<span class="text-danger">Rejected</span>';
                                                                } else if ($res_withdraw['status'] == 0) {
                                                                    $details = '<span class="text-warning">Pending</span>';
                                                                }
                                                            }


                                                        } else if ($actiontype == 'join') {
                                                            $action_name = "Bet";
                                                            $action_color = "red1";

                                                            if ($res_wallet_summery['game'] == 'wingonew' || $res_wallet_summery['game'] == 'wingo') {
                                                                // $details = "Wingo";
                                                                $details = "Colour Prediction";
                                                            } else if ($res_wallet_summery['game'] == 'aviator') {
                                                                $details = "Aviator";
                                                            }
                                                        } else if ($actiontype == 'win') {
                                                            $action_name = "Win";
                                                            $action_color = "green";

                                                            if ($res_wallet_summery['game'] == 'wingonew' || $res_wallet_summery['game'] == 'wingo') {
                                                                // $details = "Wingo";
                                                                $details = "Colour Prediction";
                                                            } else if ($res_wallet_summery['game'] == 'aviator') {
                                                                $details = "Aviator";
                                                            }
                                                        } else if ($actiontype == 'invitation_bonus') {
                                                            $action_name = "Invitation Bonus";
                                                            $action_color = "green";
                                                        } else if ($actiontype == 'self_bonus') {
                                                            $action_name = "Self Bonus";
                                                            $action_color = "green";
                                                        } else if ($actiontype == 'activity_reward') {
                                                            $action_name = "Activity Reward";
                                                            $action_color = "green";

                                                        } else if ($actiontype == 'level_up_reward') {
                                                            $action_name = "Level UP Reward";
                                                            $action_color = "green";

                                                        } else if ($actiontype == 'betting_rebate') {
                                                            $action_name = "Betting Rebate";
                                                            $action_color = "green";

                                                        } else if ($actiontype == 'transfer_to_main') {
                                                            $action_name = "Transfer Amount";
                                                            $action_color = "green";
                                                            $details = "Transfer to main wallet from safe";

                                                        } else if ($actiontype == 'transfer_from_main') {
                                                            $action_name = "Transfer Amount";
                                                            $action_color = "red";
                                                            $details = "Transfer to safe from main wallet";

                                                        } else if ($actiontype == 'gift_claimed') {
                                                            $action_name = "Gift Claimed";
                                                            $action_color = "red";

                                                            $sel_gift_code = dbQuery("SELECT * FROM tabl_gift_claim WHERE id='" . $res_wallet_summery['order_id'] . "'");
                                                            if ($res_gift_code = dbFetchAssoc($sel_gift_code)) {
                                                                $details = "Gift Code: " . $res_gift_code['gift_code'];
                                                            }
                                                        }

                                                        ?>


                                                        <tr>
                                                            <td>
                                                                <?php echo $s ?>
                                                            </td>
                                                            <td>
                                                                <?php echo date("d-m-Y h:i A", strtotime($res_wallet_summery['date'])); ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $action_name; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $details; ?>
                                                            </td>
                                                            <td class="<?php echo $action_color; ?>">₹
                                                                <?php echo $res_wallet_summery['amount']; ?>
                                                            </td>
                                                        </tr>
                                                        <?php
                                                        $s++;
                                                    }

                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->
    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $(' #zero-config').DataTable({
            "oLanguage": {
                "oPaginate": {
                    "sPrevious"
                        : '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>'
                    , "sNext"
                        : '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                }, "sInfo": "Showing page _PAGE_ of _PAGES_"
                , "sSearch"
                    : '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>'
                , "sSearchPlaceholder": "Search...", "sLengthMenu"
                    : "Results :  _MENU_",
            }, "stripeClasses":
                [], "lengthMenu": [8, 10, 20, 50], "pageLength": 10
        }); </script>
    <!-- END PAGE LEVEL SCRIPTS -->
    <script>
        /* FUNCTION FOR CONVERT NUMBER INTO abbreviation FORM */
        function numberAbbreviation(number) {
            var abbreviations = {
                "T": 1000000000000,
                "B": 1000000000,
                "M": 1000000,
                "K": 1000
            };

            for (var abbreviation in abbreviations) {
                if (Math.abs(number) >= abbreviations[abbreviation]) {
                    var formatted_number = (number / abbreviations[abbreviation]).toFixed(1);
                    return formatted_number + abbreviation;
                }
            }

            return number;
        }


        /* Function for update status */
        function change_status(id, status) {
            var retVal = confirm("Are you sure want to change status.");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/payment_method.php',
                    type: 'post',
                    data: {
                        'id': id,
                        'status': status
                    },
                    success: function (data) {
                        location.reload();
                    },
                });
            } else {
                return false;
            }
        }

        /* FUNCTION FOR DELETE  */
        function delete_qrcode(id) {
            var retVal = confirm("Are you sure want to deleted this qrcode.");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/delete_pay_type.php',
                    type: 'post',
                    data: {
                        'id': id,
                        'status': status,
                        'operation': '002'
                    },
                    success: function (data) {
                        let res = JSON.parse(data);
                        if (res.status == 'success') {
                            location.reload();
                        } else {
                            alert(res.message);
                        }
                    },
                });
            } else {
                return false;
            }
        }



        function delete_pay_type(id) {
            var retVal = confirm("Are you sure want to deleted this payment method.");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/delete_pay_type.php',
                    type: 'post',
                    data: {
                        'id': id
                    },
                    success: function (data) {
                        // let res = JSON.parse(data);
                        // if (res.status == 'success') {

                        if (data) {
                            location.reload();
                        } else {
                            // alert(res.message);

                            alert("Something went wrong!");
                        }
                    },
                });
            } else {
                return false;
            }
        }
    </script>


</body>

</html>