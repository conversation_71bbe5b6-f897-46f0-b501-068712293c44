<?php
ob_start();
session_start();
include ("../admin/lib/db_connection.php");
// include ("./lib/functions.php");


// date_default_timezone_set("Asia/Kolkata");
$date = date("Y-m-d H:i:s");

@$user_id = $_SESSION['user_id'];
@$id = $_POST['id'];
@$game_id = $_POST['game_id'];
@$game_type = $_POST['game_type'];
@$status = $_POST['status'];

if ($game_type == 1) {
    $game_duration = 1;
    $game_stop = 5;
} else if ($game_type == 2) {
    $game_duration = 3;
    $game_stop = 5;
} else if ($game_type == 3) {
    $game_duration = 5;
    $game_stop = 5;
} else if ($game_type == 4) {
    $game_duration = 10;
    $game_stop = 5;
}


$sel_result = dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_id`='$game_id' AND `game_type`='$game_type'");
if ($res_game_result = dbFetchAssoc($sel_result)) {
    if ($res_game_result['color'] == "orange+white") {
        $win_color = "orange white";
        $win_data[0]['color'] = "orange";
        $win_data[0]['name'] = "Orange";
        $win_data[1]['color'] = "secondary";
        $win_data[1]['name'] = "White";
    } else if ($res_game_result['color'] == "green+white") {
        $win_color = "green white";
        $win_data[0]['color'] = "success";
        $win_data[0]['name'] = "Green";
        $win_data[1]['color'] = "secondary";
        $win_data[1]['name'] = "White";
    } else if ($res_game_result['color'] == "orange") {
        $win_color = $res_game_result['color'];

        $win_data[0]['color'] = "orange";
        $win_data[0]['name'] = "Orange";
    } else if ($res_game_result['color'] == "green") {
        $win_color = $res_game_result['color'];

        $win_data[0]['color'] = "success";
        $win_data[0]['name'] = "Green";
    } else {
        $win_color = $res_game_result['color'];
    }
}
?>

<div class="modal-content" style="background-color: transparent;">

    <div class="card-confi card ">
        <div class="shadow-x">
            <img id="img-top" src="https://emojiisland.com/cdn/shop/products/Sad_Face_Emoji_large.png?v=1571606037"
                alt="">

            <div class="content-x d-flex justify-content-center ">
                <div class="">
                    <h4 class="text-center mb-4 text-light">Sorry</h4>
                    <p class="text-light text-center txt-h">Lottery result</p>

                    <div class="btn-x bd">
                        <!-- <div class="green btn-sam btn">Green</div> -->
                        <div class="btn-sam btn"><?php echo ucfirst($win_color); ?></div>
                        <div class="number btn  rounded-circle"><?php echo ucfirst($res_game_result['result']); ?></div>
                        <div class="small btn-sam btn"><?php echo ucfirst($res_game_result['num_type']); ?></div>
                    </div>

                    <div class="d-flex justify-content-center ">
                        <div class="bouns mt-2">
                            <!-- <p class="bouns-headline text-center">Bonus</p> -->
                            <p class="Lose text-center text-light">Lose</p>
                            <p class="text-center m-0"><small>Period <?php echo $game_duration; ?> minute
                                    <?php echo $game_id; ?></small></p>
                            <p class="close-time text-center text-light m-0">3 Seconds auto close</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-6 p-3 text-center">
                <button type="button" class="btn btn-primary" onclick="hide_win_popup();"
                    style="width: 150px">Close</button>
            </div>
        </div>

    </div>
</div>

<style>
    .card-confi {
        position: relative;
        height: 420px;
        width: 330px;
        border-radius: 10px;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        border: none;
        margin: auto;
        margin-top: 150px;
    }

    .card-confi {
        /* background: linear-gradient(90deg, rgb(60 111 175 / 91%) 0%, rgb(83 193 64 / 91%) 49%, rgb(53 131 203 / 92%) 100%); */
        /* background-color: background: rgb(60,151,175); */
        /* background: linear-gradient(90deg, rgba(60,151,175,0.9052871148459384) 0%, rgba(64,193,185,0.9080882352941176) 49%, rgba(53,171,203,0.9248949579831933) 100%);; */
        /* background: linear-gradient(127deg, rgb(52 157 242 / 91%) 0%, rgb(64 193 169 / 91%) 49%, rgb(53 203 164 / 92%) 100%); */
        /* background: linear-gradient(to right, rgb(255, 95, 109), rgb(255, 195, 113)); */
        /* background: radial-gradient(circle at 0.8% 1.8%, rgb(255, 104, 107) 0%, rgb(255, 187, 103) 90.1%); */
        background-color: #aad4e5;
    }

    #img-top {
        width: 160px;
        position: absolute;
        top: -63px;
        left: 88px;
        -webkit-filter: drop-shadow(5px 5px 5px #222);
        /* filter: drop-shadow(1px 5px 1px #2222); */
        /* filter: drop-shadow(0px 3px 2px #222); */
        filter: drop-shadow(2px 3px 5px #222);
        ;
    }

    .txt-p {
        line-height: 10px;
        letter-spacing: 1px;
        font-size: 26px;
    }

    .txt-h {
        line-height: 10px;
        letter-spacing: 1px;
        font-size: 17px;
    }

    .content-x {
        /* margin-top: 20px; */
        position: absolute;
        top: 125px;
        left: 0;
        width: 100%;
    }

    .btn-sam {
        background-color: blueviolet;

        font-size: 14px;
        color: #030303;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        /* box-shadow: rgb(204, 219, 232) 3px 3px 6px 0px inset, rgba(255, 255, 255, 0.5) -3px -3px 6px 1px inset; */
        background-color: #fffdefd4;

    }

    .btn-x {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px;
    }

    .number {
        background-color: #9f53ff7a;
        color: #fff
    }

    .bd {
        /* border: 1px solid; */
        padding: 10px;
        border-radius: 20px;
        width: 300px;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        /* background-color: #ff000017 */
    }

    .bouns p {
        color: #ffffff;
    }

    .Lose {
        font-size: 20px;
        /* color: rgb(255, 52, 52) !important; */
    }

    .bouns-headline {
        /* border: 1px solid violet; */
        padding: 7px;
        border-top-right-radius: 20px;
        border-bottom-left-radius: 20px;
        width: 200px;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        /* margin: 0; */
        /* background-color: #e3161612; */
    }

    .bouns small {
        font-size: 12px;
        color: rgb(255, 255, 255);
        /* letter-spacing: 10px; */
    }

    .shadow-x {
        height: 450px;
        /* From https://css.glass */
        /* background: rgba(255, 255, 255, 0.21);
        border-radius: 16px;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px); */
    }

    .close-time {
        font-size: 12px;
    }
</style>