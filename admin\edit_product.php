<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 5;
$sub_page = 50;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['submit'])) {
	if ($_FILES["image"]["name"] != "") {
		$target_dir = "../assets/img/products/";
		$name = rand(10000, 1000000);
		$extension = pathinfo($_FILES["image"]["name"], PATHINFO_EXTENSION);
		$new_name = $name . "." . $extension;
		$target_file = $target_dir . $name . "." . $extension;

		$imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
		if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
			die("This is not valid image. Please try again.");
		} else {
			move_uploaded_file($_FILES["image"]["tmp_name"], $target_file);
			$target_path = "../assets/img/products/" . $new_name;
			$resizeObj = new resize("../assets/img/products/" . $new_name);
			$resizeObj->resizeImage(100, 100, 'exact');
			$resizeObj->saveImage("../assets/img/products/thumb-100/" . $new_name, 100);

			$resizeObj = new resize("../assets/img/products/" . $new_name);
			$resizeObj->resizeImage(150, 150, 'exact');
			$resizeObj->saveImage("../assets/img/products/thumb-150/" . $new_name, 100);

			$resizeObj = new resize("../assets/img/products/" . $new_name);
			$resizeObj->resizeImage(512, 512, 'exact');
			$resizeObj->saveImage("../assets/img/products/thumb-500/" . $new_name, 100);



			// dbQuery("UPDATE tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',cat_id='" . $_REQUEST['cat_id'] . "',sub_cat_id='" . $_REQUEST['sub_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',image='" . $new_name . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',date_added='" . $date . "' WHERE id='" . $_REQUEST['id'] . "'");
			dbQuery("UPDATE tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',image='" . $new_name . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',date_added='" . $date . "' WHERE id='" . $_REQUEST['id'] . "'");
		}
	} else {

		// dbQuery("UPDATE tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',cat_id='" . $_REQUEST['cat_id'] . "',sub_cat_id='" . $_REQUEST['sub_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',date_added='" . $date . "' WHERE id='" . $_REQUEST['id'] . "'");
		dbQuery("UPDATE tabl_products SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',product_code='" . mysqli_real_escape_string($con, $_REQUEST['product_code']) . "',main_cat_id='" . $_REQUEST['main_cat_id'] . "',old_price='" . $_REQUEST['old_price'] . "',price='" . $_REQUEST['price'] . "',shipping_price='" . $_REQUEST['shipping_price'] . "',delivered='" . mysqli_real_escape_string($con, $_REQUEST['delivered']) . "',short_description='" . mysqli_real_escape_string($con, $_REQUEST['short_description']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',details='" . mysqli_real_escape_string($con, $_REQUEST['details']) . "',date_added='" . $date . "' WHERE id='" . $_REQUEST['id'] . "'");
	}

	$sel_stock = dbQuery("SELECT * FROM tabl_stock WHERE p_id='" . $_REQUEST['id'] . "'");
	$res_stock = dbFetchAssoc($sel_stock);
	$new_stock = $_REQUEST['qty'] + $res_stock['out_stock'];

	dbQuery("UPDATE tabl_stock SET in_stock='" . $new_stock . "' WHERE p_id='" . $_REQUEST['id'] . "'");



	$num = $_REQUEST['num'] - 1;

	for ($i = 1; $i <= $num; $i++) {
		if ($_FILES["images_" . $i]["name"] != "") {

			$target_dir = "../assets/img/products/";
			$name = rand(10000, 1000000);
			$extension = pathinfo($_FILES["images_" . $i]["name"], PATHINFO_EXTENSION);
			$new_name1 = $name . "." . $extension;
			$target_file = $target_dir . $name . "." . $extension;

			$imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
			if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
				die("This is not valid document. Please try again.");
			} else {
				move_uploaded_file($_FILES["images_" . $i]["tmp_name"], $target_file);

				$target_path = "../assets/img/products/" . $new_name1;
				$resizeObj = new resize("../assets/img/products/" . $new_name1);
				$resizeObj->resizeImage(145, 187, 'exact');
				$resizeObj->saveImage("../assets/img/products/thumb-100/" . $new_name1, 100);

				$resizeObj = new resize("../assets/img/products/" . $new_name1);
				$resizeObj->resizeImage(151, 199, 'exact');
				$resizeObj->saveImage("../assets/img/products/thumb-150/" . $new_name1, 100);

				$resizeObj = new resize("../assets/img/products/" . $new_name1);
				$resizeObj->resizeImage(518, 684, 'exact');
				$resizeObj->saveImage("../assets/img/products/thumb-500/" . $new_name1, 100);

				$qry_detail = dbQuery("insert into tabl_product_images set 										
										p_id='" . $_REQUEST['id'] . "',								
										image='" . $new_name1 . "'");
			}
		}
	}
	$color_num = $_REQUEST['color_num'] - 1;
	for ($i = 1; $i <= $color_num; $i++) {
		if ($_REQUEST['color_' . $i] != "") {
			$qry_detail = dbQuery("insert into tabl_product_options set 										
										p_id='" . $_REQUEST['id'] . "',								
										v_id='" . $_REQUEST['color_' . $i] . "',
										v_type='1'");
		}
	}

	$size_num = $_REQUEST['size_num'] - 1;
	for ($i = 1; $i <= $size_num; $i++) {
		if ($_REQUEST['size_' . $i] != "") {
			$qry_detail = dbQuery("insert into tabl_product_options set 										
										p_id='" . $_REQUEST['id'] . "',								
										v_id='" . $_REQUEST['size_' . $i] . "',
										v_type='2',
										price='" . $_REQUEST['price_' . $i] . "'");
		}
	}

	$sel_filter = dbQuery("SELECT * FROM tabl_product_options WHERE p_id='" . $_REQUEST['id'] . "'");
	$array_color = array();
	$array_size = array();
	while ($res_filter = dbFetchAssoc($sel_filter)) {
		if ($res_filter['v_type'] == 1) {
			$array_color[] = $res_filter['v_id'];
		} else {
			$array_size[] = $res_filter['v_id'];
		}
	}
	$color_filter = implode(",", $array_color);
	$size_filter = implode(",", $array_size);


	// dbQuery("UPDATE tabl_product_filter SET color='" . $color_filter . "',size='" . $size_filter . "',price='" . $_REQUEST['price'] . "', 	availablity='" . $_REQUEST['qty'] . "',sub_cat_id='" . $_REQUEST['sub_cat_id'] . "' WHERE p_id='" . $_REQUEST['id'] . "'");
	dbQuery("UPDATE tabl_product_filter SET color='" . $color_filter . "',size='" . $size_filter . "',price='" . $_REQUEST['price'] . "', 	availablity='" . $_REQUEST['qty'] . "',sub_cat_id='" . $_REQUEST['main_cat_id'] . "' WHERE p_id='" . $_REQUEST['id'] . "'");


	echo '<script>alert("Product Updated!");window.location.href="products.php"</script>';
}


$sel = dbQuery("SELECT * FROM tabl_products WHERE id='" . $_REQUEST['id'] . "'");
$res = dbFetchAssoc($sel);

$stock = dbQuery("SELECT * FROM tabl_stock WHERE p_id='" . $_REQUEST['id'] . "'");
$res_stock = dbFetchAssoc($stock);
$total_stock = $res_stock['in_stock'] - $res_stock['out_stock'];

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
	<title><?php echo SITE; ?> - Edit Products </title>
	<link rel="icon" type="image/x-icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
	<link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
	<script src="assets/js/loader.js"></script>

	<!-- BEGIN GLOBAL MANDATORY STYLES -->
	<link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
	<link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
	<link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
	<!-- END GLOBAL MANDATORY STYLES -->

	<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
	<link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
	<link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
	<!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

	<link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
	<link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

	<link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
	<link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

</head>

<body class="alt-menu sidebar-noneoverflow">

	<?php include('inc/__header.php'); ?>
	<!--  BEGIN MAIN CONTAINER  -->
	<div class="main-container" id="container">

		<div class="overlay"></div>
		<div class="search-overlay"></div>

		<!--  BEGIN TOPBAR  -->
		<?php include('inc/__menu.php'); ?>
		<!--  END TOPBAR  -->

		<!--  BEGIN CONTENT PART  -->
		<div id="content" class="main-content">
			<div class="layout-px-spacing">
				<nav class="breadcrumb-one" aria-label="breadcrumb">
					<ol class="breadcrumb">
						<li class="breadcrumb-item"><a href="index.php">Home</a></li>
						<li class="breadcrumb-item"><a href="products.php">Products</a></li>
						<li class="breadcrumb-item active"><a href="javascript:void(0);">Edit</a></li>

					</ol>
				</nav>
				<div class="account-settings-container layout-top-spacing">

					<div class="account-content">
						<div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
							<div class="row">
								<div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
									<form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">

										<div class="info">
											<h6 class="">Edit Product</h6>
											<div class="row">
												<div class="col-lg-11 mx-auto">
													<div class="row">
														<div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
															<div class="form">
																<div class="form-row">
																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Name</strong></label>
																		<input type="text" class="form-control" value="<?php echo $res['name']; ?>" id="Name" name="name" placeholder="Name" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Product Code</strong></label>
																		<input type="text" class="form-control" id="product_code" value="<?php echo $res['product_code']; ?>" name="product_code" placeholder="Product Code" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Qty</strong></label>
																		<input type="text" class="form-control" id="qty" name="qty" value="<?php echo $total_stock; ?>" placeholder="Quantity" onkeypress="return isDecimal(event,this)" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Main-Category</strong></label>
																		<select name="main_cat_id" class="form-control" onChange="set_cat(this.value)" required>
																			<option value="">SELECT</option>
																			<?php $main_cat = dbQuery("SELECT * FROM tabl_main_category ORDER BY name ASC");
																			while ($res_main_cat = dbFetchAssoc($main_cat)) {
																				if ($res_main_cat['id'] == $res['main_cat_id']) {
																					$selected = 'selected';
																				} else {
																					$selected = '';
																				}
																			?>
																				<option value="<?php echo $res_main_cat['id']; ?>" <?php echo $selected; ?>><?php echo $res_main_cat['name']; ?></option>
																			<?php } ?>
																		</select>
																	</div>




																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>MRP Price</strong></label>
																		<input type="text" class="form-control" id="Name" name="old_price" value="<?php echo $res['old_price']; ?>" placeholder="MRP Price" onkeypress="return isDecimal(event,this)" required>
																	</div>
																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Offer Price</strong></label>
																		<input type="text" class="form-control" id="Name" name="price" value="<?php echo $res['price']; ?>" placeholder="Offer Price" onkeypress="return isDecimal(event,this)" required>
																	</div>

																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Delivery Price</strong></label>
																		<input type="text" class="form-control" id="shipping_price" value="<?php echo $res['shipping_price']; ?>" name="shipping_price" placeholder="Delivery Price" onkeypress="return isDecimal(event,this)" required>
																	</div>
																	<div class="form-group col-md-6">
																		<label for="fullName"><strong>Delivered</strong></label>
																		<input type="text" class="form-control" id="delivered" name="delivered" value="<?php echo $res['delivered']; ?>" placeholder="Exp: 2-5 Business Days" required>
																	</div>

																</div>

																<div class="row">
																	<div class="col-sm-6">
																		<div class="form-group">
																			<label for="fullName"><strong>Short Description</strong></label>
																			<textarea class="form-control" id="short_description" name="short_description" placeholder="Short Description" required rows="6"><?php echo $res['short_description']; ?></textarea>
																		</div>

																	</div>

																	<div class="col-sm-6">
																		<div class="form-group">
																			<label for="fullName"><strong>Full Description</strong></label>
																			<textarea class="form-control" id="description" name="description" placeholder="Description" required rows="6"><?php echo $res['description']; ?></textarea>
																		</div>

																	</div>

																	<div class="col-sm-12">
																		<div class="form-group">
																			<label for="fullName"><strong>Details</strong></label>
																			<textarea class="form-control" id="details" name="details" placeholder="Details" required rows="6"><?php echo $res['details']; ?></textarea>
																		</div>

																	</div>
																	<div class="col-sm-12">
																		<div class="form-group">
																			<img src="../assets/img/products/thumb-100/<?php echo $res['image']; ?>" width="80">
																		</div>
																		<div class="form-group">
																			<label for="fullName">Update Cover Image</label>
																			<input type="file" class="form-control mb-4" id="Name" name="image" placeholder="Image">
																		</div>
																	</div>

																	<div class="col-xl-12 col-md-12 col-sm-12 col-12">
																		<h5 style="color:#d06a6a">Add More Images</h5>
																	</div>
																	<table class="table table-bordered table-striped">
																		<tr>
																			<th>
																				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;">
																					<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
																					<line x1="12" y1="8" x2="12" y2="16"></line>
																					<line x1="8" y1="12" x2="16" y2="12"></line>
																				</svg>
																			</th>
																			<th>S.No.</th>
																			<th>Images</th>
																		</tr>
																		<tbody id="classes">
																		</tbody>
																	</table>
																	<table class="table table-bordered table-striped">
																		<tr>
																			<th>S.No.</th>
																			<th>Images</th>
																			<th>Delete</th>
																		</tr>
																		<?php $sel_image = dbQuery("SELECT * FROM `tabl_product_images` WHERE p_id='" . $_REQUEST['id'] . "'");
																		$i = 1;
																		while ($res_image = dbFetchAssoc($sel_image)) { ?>
																			<tr class="image_rows_<?php echo $i; ?>">
																				<td><?php echo $i; ?></td>
																				<td><img src="../assets/img/products/thumb-100/<?php echo $res_image['image']; ?>" width="50"></td>
																				<td><a href="javascript:void(0)" onClick="delete_product_image(<?php echo $res_image['id'] ?>,<?php echo $i ?>)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2">
																							<polyline points="3 6 5 6 21 6"></polyline>
																							<path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
																							<line x1="10" y1="11" x2="10" y2="17"></line>
																							<line x1="14" y1="11" x2="14" y2="17"></line>
																						</svg></a></td>
																			</tr>
																		<?php $i++;
																		} ?>
																	</table>



																	<!-- <div class="col-xl-12 col-md-12 col-sm-12 col-12">
		<h5 style="color:#d06a6a">Add Colors</h5>
	</div>
	
		<table class="table table-bordered table-striped">
		    <tr>
			    <th>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayColor()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
            </th>
			   <th>S.No.</th>
			   <th>Colors</th>
			</tr>
		 <tbody id="colors">
          </tbody>
		</table>	

<table class="table table-bordered table-striped">
		    <tr>
			   <th>S.No.</th>
			   <th>Colors</th>
			   <th>Delete</th>
			</tr>
		<?php
		$sel_color = dbQuery("SELECT tabl_product_options.*,tabl_product_options.id as po_id,tabl_product_variation_value.* FROM  tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_options.v_id=tabl_product_variation_value.id WHERE tabl_product_options.p_id='" . $_REQUEST['id'] . "' AND tabl_product_options.v_type=1");
		$i = 1;
		while ($res_color = dbFetchAssoc($sel_color)) { ?> 
		     <tr class="color_row_<?php echo $i; ?>">
			    <td><?php echo $i; ?></td>
				<td><?php echo $res_color['v_value']; ?></td>
				 <td><a href="javascript:void(0)" onClick="delete_product_color(<?php echo $res_color['po_id'] ?>,<?php echo $i; ?>,<?php echo $_REQUEST['id'] ?>)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg></a></td>  
			 </tr>
				  <?php $i++;
				} ?> 
		</table>		 -->

																	<!-- <div class="col-xl-12 col-md-12 col-sm-12 col-12">
																		<h5 style="color:#d06a6a">Add Quantity</h5>
																	</div>
																	<table class="table table-bordered table-striped">
																		<tr>
																			<th>
																				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displaySize()" name="add" style="cursor:pointer;">
																					<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
																					<line x1="12" y1="8" x2="12" y2="16"></line>
																					<line x1="8" y1="12" x2="16" y2="12"></line>
																				</svg>
																			</th>
																			<th>S.No.</th>
																			<th>Quantity</th>
																			<th>Price</th>
																		</tr>
																		<tbody id="sizes">
																		</tbody>
																	</table>
																	<table class="table table-bordered table-striped">
																		<tr>
																			<th>S.No.</th>
																			<th>Quantity</th>
																			<th>Price</th>
																			<th>Delete</th>
																		</tr>
																		<?php
																		$sel_size = dbQuery("SELECT tabl_product_options.*,tabl_product_options.id as po_id,tabl_product_variation_value.* FROM  tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_options.v_id=tabl_product_variation_value.id WHERE tabl_product_options.p_id='" . $_REQUEST['id'] . "' AND tabl_product_options.v_type=2");
																		$i = 1;
																		while ($res_size = dbFetchAssoc($sel_size)) { ?>
																			<tr class="size_row_<?php echo $i ?>">
																				<td><?php echo $i; ?></td>
																				<td><?php echo $res_size['v_value']; ?></td>
																				<td><?php echo $res_size['price']; ?></td>
																				<td><a href="javascript:void(0)" onClick="delete_product_size(<?php echo $res_size['po_id'] ?>,<?php echo $i ?>,<?php echo $_REQUEST['id'] ?>)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2">
																							<polyline points="3 6 5 6 21 6"></polyline>
																							<path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
																							<line x1="10" y1="11" x2="10" y2="17"></line>
																							<line x1="14" y1="11" x2="14" y2="17"></line>
																						</svg></a></td>
																			</tr>
																		<?php $i++;
																		} ?>
																	</table> -->



																	<div class="col-sm-12">
																		<div class="form-group">
																			<label for="fullName"></label>
																			<input type="hidden" name="num" id="num" value="1" />
																			<input type="hidden" name="color_num" id="color_num" value="1" />
																			<input type="hidden" name="size_num" id="size_num" value="1" />

																			<button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Submit</button>
																		</div>

																	</div>



																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>

									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
				<?php include('inc/__footer.php'); ?>
			</div>

		</div>
		<!--  END CONTENT PART  -->

	</div>
	<!-- END MAIN CONTAINER -->

	<!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
	<script src="assets/js/libs/jquery-3.1.1.min.js"></script>
	<script src="bootstrap/js/popper.min.js"></script>
	<script src="bootstrap/js/bootstrap.min.js"></script>
	<script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
	<script src="assets/js/app.js"></script>
	<script>
		$(document).ready(function() {
			App.init();
		});
	</script>
	<script src="assets/js/custom.js"></script>
	<!-- END GLOBAL MANDATORY SCRIPTS -->

	<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
	<script src="plugins/apex/apexcharts.min.js"></script>
	<script src="assets/js/dashboard/dash_2.js"></script>
	<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

	<script src="plugins/dropify/dropify.min.js"></script>
	<script src="plugins/blockui/jquery.blockUI.min.js"></script>
	<!-- <script src="plugins/tagInput/tags-input.js"></script> -->
	<script src="assets/js/users/account-settings.js"></script>
	<script>
		CKEDITOR.replace('description');
	</script>
	<script>
		CKEDITOR.replace('short_description');
	</script>
	<script>
		CKEDITOR.replace('details');
	</script>


	<script>
		function delete_product_color(id, row_id, p_id) {
			var retVal = confirm("Are you sure want to delete.");
			if (retVal == true) {
				$.ajax({
					url: 'ajax/delete_product_color.php',
					type: 'post',
					data: {
						'id': id,
						'p_id': p_id
					},
					success: function(data) {
						$(".color_row_" + row_id).hide('');
					},
				});
			} else {
				return false;
			}
		}
	</script>


	<script>
		function delete_product_size(id, row_id, p_id) {
			var retVal = confirm("Are you sure want to delete.");
			if (retVal == true) {
				$.ajax({
					url: 'ajax/delete_product_size.php',
					type: 'post',
					data: {
						'id': id,
						'p_id': p_id
					},
					success: function(data) {
						$(".size_row_" + row_id).hide('');
					},
				});
			} else {
				return false;
			}
		}
	</script>


	<script>
		function delete_product_image(id, row_id) {
			var retVal = confirm("Are you sure want to delete.");
			if (retVal == true) {
				$.ajax({
					url: 'ajax/delete_product_images.php',
					type: 'post',
					data: {
						'id': id
					},
					success: function(data) {
						$(".image_rows_" + row_id).hide('');
					},
				});
			} else {
				return false;
			}
		}
	</script>


	<script>
		function displayResult()

		{

			var i = document.getElementById("num").value;

			document.getElementById("classes").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><input type="file" name="images_' + i + '" id="images_' + i + '"  class="form-control" accept="image/*"></td></tr>';
			i++;

			var num = document.getElementById("num").value = i;

		}
	</script>

	<script>
		function displayColor() {
			var i = document.getElementById("color_num").value;
			document.getElementById("colors").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayColor()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><select name="color_' + i + '" id="color_' + i + '" class="form-control"><?php echo get_all_color(); ?></select></td></tr>';
			i++;
			var color_num = document.getElementById("color_num").value = i;
		}
	</script>


	<script>
		function displaySize()

		{

			var i = document.getElementById("size_num").value;

			document.getElementById("sizes").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displaySize()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><select name="size_' + i + '" id="size_' + i + '" class="form-control"><?php echo get_all_size(); ?></select></td><td><input type="text" name="price_' + i + '" id="price_' + i + '" class="form-control"  onkeypress="return isDecimal(event,this)"></td></tr>';
			i++;

			var color_num = document.getElementById("size_num").value = i;

		}
	</script>

	<script>
		function isNumber(evt) {
			var iKeyCode = (evt.which) ? evt.which : evt.keyCode
			if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
				return false;

			return true;
		}

		function isDecimal(evt, obj) {

			var charCode = (evt.which) ? evt.which : event.keyCode
			var value = obj.value;
			var dotcontains = value.indexOf(".") != -1;
			if (dotcontains)
				if (charCode == 46) return false;
			if (charCode == 46) return true;
			if (charCode > 31 && (charCode < 48 || charCode > 57))
				return false;
			return true;
		}
	</script>

	<script>
		function set_cat(main_cat_id) {
			if (main_cat_id != "") {
				$.ajax({
					url: 'ajax/get_category.php',
					type: 'post',
					data: {
						'cat_id': main_cat_id
					},
					success: function(data) {
						if (data != "") {
							$("#cat_id").html(data);
							$("#sub_cat_id").html('<option value="">SELECT</option>');
						}
					},
				});
			} else {
				$("#cat_id").html('<option value="">SELECT</option>');
				$("#sub_cat_id").html('<option value="">SELECT</option>');
			}
		}
	</script>

	<script>
		function set_sub_cat(cat_id) {
			if (cat_id != "") {
				$.ajax({
					url: 'ajax/get_sub_category.php',
					type: 'post',
					data: {
						'cat_id': cat_id
					},
					success: function(data) {
						if (data != "") {
							$("#sub_cat_id").html(data);
						}
					},
				});
			} else {
				$("#sub_cat_id").html('<option value="">SELECT</option>');
			}
		}
	</script>


	<script>
		function isNumber(evt) {
			var iKeyCode = (evt.which) ? evt.which : evt.keyCode
			if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
				return false;

			return true;
		}

		function isDecimal(evt, obj) {

			var charCode = (evt.which) ? evt.which : event.keyCode
			var value = obj.value;
			var dotcontains = value.indexOf(".") != -1;
			if (dotcontains)
				if (charCode == 46) return false;
			if (charCode == 46) return true;
			if (charCode > 31 && (charCode < 48 || charCode > 57))
				return false;
			return true;
		}
	</script>



</body>

</html>