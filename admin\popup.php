<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');

$page = 6;
$sub_page = 65;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Check if an ID is provided for editing

$popup_id = 1;


// Update the popup if form is submitted
if (isset($_REQUEST['submit'])) {
    $title = $_REQUEST['title'];
    $description = $_REQUEST['description'];
    $link = $_REQUEST['link'];
    $status = $_REQUEST['status'];

    // Update the existing popup
    $update_query = "UPDATE `tabl_popup` SET 
                        `title` = '" . $title . "',
                        `description` = '" . $description . "',
                        `link` = '" . $link . "',
                        `status` = '" . $status . "',
                        `date` = '" . $date . "'
                     WHERE `id` = '" . $popup_id . "'";

    $status = dbQuery($update_query);

    if ($status) {
        echo "<script>alert('Popup updated successfully!'); window.location.href = 'popup.php';</script>";
    } else {
        echo "<script>alert('Something went wrong!'); window.location.href = 'popup.php';</script>";
    }
}


// Fetch existing popup details
$popup = dbFetchAssoc(dbQuery("SELECT * FROM `tabl_popup` WHERE `id` = '$popup_id'"));

if (!$popup) {
    echo "<script>alert('Popup not found!'); window.location.href = 'popup.php';</script>";
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Add Level
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

</head>

<body class="alt-menu sidebar-noneoverflow">
    <?php include('inc/__header.php'); ?>
    <div class="main-container" id="container">
        <?php include('inc/__menu.php'); ?>

        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <div class="account-settings-container layout-top-spacing">
                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">
                                        <div class="info">
                                            <h6 class="">Edit Popup</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">
                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <!-- <label for="title">Title</label>
                                                                            <input type="text" class="form-control mb-4" id="title" name="title" value="<?php echo $popup['title']; ?>" required> -->

                                                                            <div class="form-group">
                                                                                <label for="title">Title</label>
                                                                                <textarea class="form-control mb-4" id="title" name="title" required><?php echo $popup['title']; ?></textarea>
                                                                            </div>

                                                                        </div>
                                                                    </div>


                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <label for="link">Link</label>
                                                                            <input type="text" class="form-control mb-4" id="link" name="link" value="<?php echo $popup['link']; ?>" required>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <label for="status">Status</label>
                                                                            <select class="form-control mb-4" id="status" name="status" required>
                                                                                <option value="1" <?php echo ($popup['status'] == '1') ? 'selected' : ''; ?>>Active</option>
                                                                                <option value="0" <?php echo ($popup['status'] == '0') ? 'selected' : ''; ?>>Inactive</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>



                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="description">Description</label>
                                                                            <textarea class="form-control mb-4" id="description" name="description" required><?php echo $popup['description']; ?></textarea>
                                                                        </div>
                                                                    </div>



                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Update Popup</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>
        </div>
    </div>
</body>

</html>