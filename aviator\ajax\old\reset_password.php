<?php
session_start();
include('../admin/lib/db_connection.php');

if (@$_SESSION['user_id'] == "") {
  echo "<script>window.location.href='./login.php';</script>";
}
$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
$user = dbFetchAssoc($userq);

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');


$old_password = $_REQUEST['old_password'];

$passcheck = password_verify($old_password, $user['password']);

if ($passcheck) {

  $new_password = $_REQUEST['new_password'];
  $con_password = $_REQUEST['con_password'];

  if ($new_password == $con_password) {

    $password = password_hash($new_password, PASSWORD_BCRYPT);

    $upd = dbQuery("UPDATE tabl_user Password SET password='" . $password . "' WHERE id='" . $user['id'] . "'");
    if ($upd) {
      echo 1;
    } else {
      echo 0;
    }
  } else {
    echo 3;
  }
} else {
  echo 2;
}
