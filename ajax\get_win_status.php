<?php
ob_start();
session_start();
include ("../admin/lib/db_connection.php");
// include ("./lib/functions.php");


// date_default_timezone_set("Asia/Kolkata");
$date = date("Y-m-d H:i:s");

@$user_id = $_SESSION['user_id'];
@$game_type = $_POST['game_type'];


if ($user_id == "" || $game_type == "") {
  // echo "2";

  echo json_encode(array("status" => "error", "message" => "User ID or game type is missing"));
  //check empty
} else {

  $last_game_id_query = dbQuery("SELECT MAX(game_id) AS last_game_id FROM `tabl_wingonew_result` WHERE `game_type`='$game_type'");
  if ($last_game_id_result = dbFetchAssoc($last_game_id_query)) {
    $last_game_id = $last_game_id_result['last_game_id'];

    // Fetch data from the database
    $sel_userresult = dbQuery("SELECT id, `status`, game_id, game_type FROM `tabl_wingonew_userresult` WHERE `game_id`='$last_game_id' AND `user_id`='$user_id' AND `game_type`='$game_type'");

    // Initialize an array to store the fetched data
    $results = array();

    // Loop through the fetched results and add them to the array
    while ($row = dbFetchAssoc($sel_userresult)) {
      $results[] = $row;
    }

    // Encode the data into JSON format
    $json_data = json_encode($results);

    // Return the status and data in JSON format
    echo json_encode(array("status" => "success", "data" => $results));

  } else {
    echo json_encode(array("status" => "error", "message" => "Something went wrong!"));
  }
}
