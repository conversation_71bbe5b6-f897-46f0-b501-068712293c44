<?php
ob_start();
session_start();
if ($_SESSION['user_id'] == "") {
    header("location:login.php");
    exit();
}
// echo $_SESSION['user_id'];

include ('../admin/lib/db_connection.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');


// Function to send JSON response
function json_response($data, $isSuccess, $message)
{
    $res = array(
        "data" => $data,
        "isSuccess" => $isSuccess,
        "message" => $message
    );
    return json_encode($res, JSON_PRETTY_PRINT);
}

// Function to fetch user details
function get_user_detail()
{
    // Initialize variables
    $data = "";
    $isSuccess = false;
    $message = "Something went wrong!";

    // Check if user is logged in
    if (!isset($_REQUEST['id']) || empty($_REQUEST['id'])) {
        return json_response($data, $isSuccess, "User not logged in");
    }

    // Assuming dbQuery and dbFetchAssoc are defined elsewhere
    // Fetch user details from database
    $sel_level = dbQuery("SELECT * FROM tabl_level WHERE id='" . $_REQUEST['id'] . "'");

    // If user details found
    if ($res_level = dbFetchAssoc($sel_level)) {
        $data = array(
            // "username" => $res_level['id'],
            "title" => $res_level['title'],
            "level_up_reward" => $res_level['level_up_reward'],
            "monthly_reward" => $res_level['monthly_reward'],
            "safe_income" => $res_level['safe_income'],
            "rebate_rate" => $res_level['rebate_rate'],
            "notification" => ""
        );
        $message = "Success";
        $isSuccess = true;
    } else {
        $message = "Level data not found";
    }

    // Return JSON response
    return json_response($data, $isSuccess, $message);
}

// Example usage:
echo get_user_detail();