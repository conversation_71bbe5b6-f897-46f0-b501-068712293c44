<?php

session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');

$page = 5;
$sub_page = 50;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

if (isset($_REQUEST['submit'])) {
    $bank_name = mysqli_real_escape_string($con, $_REQUEST['bank_name']);
    $type = mysqli_real_escape_string($con, $_REQUEST['type']);
    $pay_type = mysqli_real_escape_string($con, $_REQUEST['pay_type']);
    $upi_id = mysqli_real_escape_string($con, $_REQUEST['upi_id']);

    $target_dir = "../assets/img/qr/";
    // $target_dir = "assets/img/qr_codes/";

    $img_name = rand(10000, 1000000);

    $extension = pathinfo($_FILES["image"]["name"], PATHINFO_EXTENSION);
    $new_name = $img_name . "." . $extension;
    $target_file = $target_dir . $img_name . "." . $extension;

    $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
    if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
        die("This is not valid image. Please try again.");
    } else {
        move_uploaded_file($_FILES["image"]["tmp_name"], $target_file);

        $duplicateQuery = dbQuery("SELECT * FROM tabl_rechargesetting WHERE `upi_id` = '$upi_id'");
        if (dbNumRows($duplicateQuery)) {
            // die("Duplicate data: Data Already Exist");
            echo '<script>alert("Duplicate entry!");window.location.href="payment_setting.php"</script>';
        } else {
            $result = dbQuery("INSERT INTO tabl_rechargesetting SET bank_name='" . $bank_name . "',type='" . $type . "',pay_type='" . $pay_type . "',upi_id='" . $upi_id . "',image='" . $new_name . "',status='1'");

            // $insertQuery = dbQuery("INSERT INTO tabl_qrcode(`qrcode_name`, `image`,`pay_type`,`pay_id`,`qrcode_status`) VALUES ('$name','$new_name','$pay_type','$pay_id',1)");
            echo '<script>alert("Payment Method Added!");window.location.href="payment_setting.php"</script>';
        }
    }

}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Add Payment Method
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>



</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="payment-settings.php">Payment Settings</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Add</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info"
                                        enctype="multipart/form-data">
                                        <div class="info">
                                            <h6 class="">Add Payment Method</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-12 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">
                                                                    <!-- type -->
                                                                    <div class="form-group col-sm-6">
                                                                        <label class="form-label" for="bank_name">Method
                                                                            Name</label>
                                                                        <input type="text" class="form-control"
                                                                            name="bank_name" id="bank_name"
                                                                            placeholder="Enter Method Name" required>
                                                                    </div>
                                                                    <!-- pay_type -->
                                                                    <div class="form-group col-sm-6 mb-2">
                                                                        <label class="form-label" for="type">Method
                                                                            Type</label>
                                                                        <select class="form-control" name="type"
                                                                            id="type">
                                                                            <!-- <option value="bank">Bank</option> -->
                                                                            <option value="upi">UPI/USDT</option>
                                                                        </select>
                                                                    </div>

                                                                    <!-- pay_type -->
                                                                    <div class="form-group col-sm-6 mb-2">
                                                                        <label class="form-label" for="pay_type">Pay
                                                                            Type</label>
                                                                        <select class="form-control" name="pay_type"
                                                                            id="pay_type">
                                                                            <option value="INR">INR</option>
                                                                            <option value="USDT">USDT</option>
                                                                        </select>
                                                                    </div>


                                                                    <!-- payid -->
                                                                    <div class="form-group col-sm-6 mb-2">
                                                                        <label class="form-label"
                                                                            for="upi_id">UPI/Payment Id</label>
                                                                        <input type="text" class="form-control"
                                                                            name="upi_id" id="upi_id"
                                                                            placeholder="Enter UPI/VPA Id" required>
                                                                    </div>

                                                                    <!-- image -->
                                                                    <div class="form-group col-sm-12 mb-2">
                                                                        <label class="form-label" for="image">Qr
                                                                            Code Image
                                                                        </label>
                                                                        <input type="file" class="form-control"
                                                                            name="image" id="image"
                                                                            placeholder="Choose image..."
                                                                            accept="image/*" required>
                                                                    </div>


                                                                    <div class="action col-sm-12">
                                                                        <button type="submit" name="submit"
                                                                            class="btn btn-secondary mb-4">Submit</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>