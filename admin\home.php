<?php

session_start();

include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');

include ('inc/resize-class.php');


$page = 1;
$sub_page = 0;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
  <title><?php echo SITE; ?>- DashBoard</title>
  <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
  <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
  <script src="assets/js/loader.js"></script>

  <!-- BEGIN GLOBAL MANDATORY STYLES -->

  <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
  <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />

  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
  <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />

  <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

  <style>
    .home_section {
      color: white !important;
    }

    .widget-account-invoice-one {
      padding: 22px 19px;
      background: #e2a03f;
      background: linear-gradient(to right, #6b7764 0%, #2f384f 100%);
    }

    .widget-account-invoice-two {
      padding: 22px 19px;
      background: #e2a03f;
      background: linear-gradient(to right, #6b7764 0%, #2f384f 100%);
    }

    .widget-account-invoice-three {
      padding: 22px 19px;
      background: #e2a03f;
      background: linear-gradient(to right, #485d8c 0%, #0849ef 100%);
    }


    .widget-account-invoice-four {
      padding: 22px 19px;
      background: #e2a03f;
      background: linear-gradient(to right, #415d48 0%, #1bdc68 100%);
    }

    .widget-account-invoice-five {
      padding: 22px 19px;
      background: #e2a03f;
      background: linear-gradient(to right, #d7da37 0%, #dcaa1b 100%);
    }


    .widget-account-invoice-six {
      padding: 22px 19px;
      background: #e2a03f;
      background: linear-gradient(to right, #b17276 0%, #dc1b1b 100%);
    }

    .pointer {
      cursor: pointer;
    }
  </style>

</head>

<body class="alt-menu sidebar-noneoverflow">
  <?php include ('inc/__header.php'); ?>

  <!--  BEGIN MAIN CONTAINER  -->

  <div class="main-container" id="container">
    <div class="overlay"></div>
    <div class="search-overlay"></div>

    <!--  BEGIN TOPBAR  -->

    <?php include ('inc/__menu.php'); ?>

    <!--  END TOPBAR  -->

    <!--  BEGIN CONTENT PART  -->

    <div id="content" class="main-content">
      <div class="layout-px-spacing">
        <div class="row layout-top-spacing">

          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing d-none">
            <div class="widget-four">

              <div class="widget-heading">
                <h2 class="text-center pt-2">Welcome To <?php echo SITE; ?></h2>

              </div>
            </div>
          </div>

          <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 layout-spacing pointer"
            onClick="window.location.href='user.php'">
            <div class="widget widget-account-invoice-one">
              <div class="widget-content">
                <div class="account-box">
                  <div class="info">
                    <div class="inv-title">
                      <?php

                      $sel_user = dbQuery("SELECT * FROM tabl_user");
                      $total_user = dbNumRows($sel_user);
                      ?>
                      <h4 class="home_section">Total User<br />
                        <?php echo $total_user; ?>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


          <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 layout-spacing pointer"
            onClick="window.location.href='user.php'">
            <div class="widget widget-account-invoice-six">
              <div class="widget-content">
                <div class="account-box">
                  <div class="info">
                    <div class="inv-title">
                      <?php

                      $sel_user = dbQuery("SELECT * FROM tabl_user");
                      $active_user = 0;
                      $inactive_user = 0;
                      while ($res_user = dbFetchAssoc($sel_user)) {
                        if ($res_user['status'] == 1) {
                          $active_user += 1;
                        } else {
                          $inactive_user += 1;
                        }
                      }
                      ?>
                      <h4 class="home_section">Active User<br />
                        <?php echo $active_user; ?>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 layout-spacing pointer"
            onClick="window.location.href='pending_recharge.php'">
            <div class="widget widget-account-invoice-three">
              <div class="widget-content">
                <div class="account-box">
                  <div class="info">
                    <div class="inv-title">
                      <h4 class="home_section">Pending Recharge<br />
                        <?php $sel_recharge = dbQuery("SELECT * FROM tabl_deposits WHERE `status`=0");
                        $num_recharge = dbNumRows($sel_recharge);
                        echo $num_recharge;
                        ?>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 layout-spacing pointer"
            onClick="window.location.href='pending_withdrawal.php'">
            <div class="widget widget-account-invoice-four">
              <div class="widget-content">
                <div class="account-box">
                  <div class="info">
                    <div class="inv-title">
                      <h4 class="home_section">Pending Withdrawal<br />
                        <?php $sel_withdrawal = dbQuery("SELECT * FROM tabl_withdrawal WHERE `status`=0");
                        $num_withdrawal = dbNumRows($sel_withdrawal);
                        echo $num_withdrawal;
                        ?>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


          <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 layout-spacing pointer"
            onClick="window.location.href='pending_recharge.php'">
            <div class="widget widget-account-invoice-six">
              <div class="widget-content">
                <div class="account-box">
                  <div class="info">
                    <div class="inv-title">
                      <?php

                      $sel_deposits = dbQuery("SELECT SUM(amount) FROM tabl_deposits WHERE DATE(`date`)='$date'");
                      $res_deposits = dbFetchAssoc($sel_deposits);
                      $total_deposits = $res_deposits['SUM(amount)'];
                      if ($total_deposits == '') {
                        $total_deposits = 0;
                      }
                      ?>
                      <h4 class="home_section">Today Rechrge<br />
                        <?php echo $total_deposits; ?>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 layout-spacing pointer"
            onClick="window.location.href='pending_withdrawal.php'">
            <div class="widget widget-account-invoice-three">
              <div class="widget-content">
                <div class="account-box">
                  <div class="info">
                    <div class="inv-title">
                      <?php

                      $sel_withdrawal = dbQuery("SELECT SUM(amount) FROM tabl_withdrawal WHERE DATE(`date`)='$date'");
                      $res_withdrawal = dbFetchAssoc($sel_withdrawal);
                      $total_withdrawal = $res_withdrawal['SUM(amount)'];

                      if ($total_withdrawal == '') {
                        $total_withdrawal = 0;
                      }
                      ?>

                      <h4 class="home_section">Today Withdrawal<br />
                        <?php echo $total_withdrawal; ?>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>



          <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 layout-spacing pointer"
            onClick="window.location.href='payment_setting.php'">
            <div class="widget widget-account-invoice-five">
              <div class="widget-content">
                <div class="account-box">
                  <div class="info">
                    <div class="inv-title">
                      <?php

                      // $sel_rechargesetting = dbQuery("SELECT * FROM tabl_rechargesetting WHERE DATE(`date`)='$date'");
                      $sel_rechargesetting = dbQuery("SELECT * FROM tabl_rechargesetting");
                      $num_rechargesetting = dbNumRows($sel_rechargesetting);
                      // $total_rechargesetting = $res_rechargesetting['SUM(amount)'];
                      

                      // $total_bids = $total_main_bids + $total_sp_bids;
                      ?>
                      <h4 class="home_section">Payment Methods<br />
                        <?php echo $num_rechargesetting; ?>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>



        </div>
        <?php include ('inc/__footer.php'); ?>
      </div>
    </div>

    <!--  END CONTENT PART  -->

  </div>

  <!-- END MAIN CONTAINER -->

  <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->

  <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
  <script src="bootstrap/js/popper.min.js"></script>
  <script src="bootstrap/js/bootstrap.min.js"></script>
  <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
  <script src="assets/js/app.js"></script>
  <script>
    $(document).ready(function () {


      App.init();


    });
  </script>


  <script src="assets/js/custom.js"></script>

  <!-- END GLOBAL MANDATORY SCRIPTS -->

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

  <script src="plugins/apex/apexcharts.min.js"></script>
  <script src="assets/js/dashboard/dash_2.js"></script>

  <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

</body>

</html>

<script>
  function delete_entry(tabl, val, row_id) {
    var retVal = confirm("Are you sure want to delete this enrty.");
    if (retVal == true) {
      $.ajax({
        url: 'ajax/activate.php',
        type: 'post',
        data: {
          'tabl': tabl,
          'val': val,
          'row_id': row_id
        },
        success: function (data) {
          //alert(data);
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }


  }


  function return_order_success(order_id, row_id) {
    var retVal = confirm("Are you sure want to Return this order.");
    if (retVal == true) {
      $(".return_order_" + row_id).html('<img src="../loader.gif" style="width: 10px;"></i> please wait...');
      $.ajax({
        url: 'ajax/return_order_success.php',
        type: 'post',
        data: {
          'order_id': order_id
        },
        success: function (data) {
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }
  }
</script>