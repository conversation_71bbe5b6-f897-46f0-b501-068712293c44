<?php
include ("../admin/lib/db_connection.php");

$game_type = $_POST['game_type'];

$today = date('Y-m-d');

// Number of items per page
$itemsPerPage = 10;

// Current page
$currentPage = isset($_POST['page']) ? $_POST['page'] : 1;
$offset = ($currentPage - 1) * $itemsPerPage;
// $currentPage = 5;

// Calculate the date for two days ago
$dateTwoDaysAgo = date('Y-m-d', strtotime('-2 days'));

// Database query with pagination
$query = "SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' AND DATE(`date`) >= '$dateTwoDaysAgo' AND DATE(`date`) <= '$today' ORDER BY id DESC LIMIT $offset, $itemsPerPage";
$parityrecordQuery = dbQuery($query);

// Total number of rows
$totalRows = dbNumRows(dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' AND DATE(`date`) >= '$dateTwoDaysAgo' AND DATE(`date`) <= '$today'"));

// Calculate total number of pages
$totalPages = ceil($totalRows / $itemsPerPage);

// $totalPages = 20;

?>

<div class="table-container">
    <!-- Display data table -->
    <table class="table">
        <!-- Table header -->
        <thead>
            <tr>
                <!-- Table headers -->
                <th style="background-color: #0a58ca; color: #fff;">Period</th>
                <th style="background-color: #0a58ca; color: #fff;">Number</th>
                <th style="background-color: #0a58ca; color: #fff;">Big Small</th>
                <th style="background-color: #0a58ca; color: #fff;">Color</th>
            </tr>
        </thead>
        <!-- Table body -->
        <tbody>
            <!-- Loop through fetched rows -->
            <?php
            while ($parityResult = dbFetchAssoc($parityrecordQuery)) {
                // Display table rows
                // (Your existing code here)
            

                $result = $parityResult['result'];
                $color = $parityResult['color'];

                ?>
                <tr>
                    <!-- <td><?php echo substr($parityResult['game_id'], 8); ?></td> -->
                    <td>
                        <?php echo $parityResult['game_id']; ?>
                    </td>
                    <td>
                        <?php echo $parityResult['result']; ?>
                    </td>
                    <td>
                        <?php echo $parityResult['num_type']; ?>
                    </td>
                    <td>
                        <?php
                        if ($color == 'orange') {

                            ?>
                            <i class="fa-solid fa-circle"
                                style="color: rgb(255, 123, 0);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                            </i>
                            <?php
                        } else if ($color == 'green') {
                            ?>
                                <i class="fa-solid fa-circle"
                                    style="color: green;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                </i>
                            <?php
                        } else if ($color == 'orange+white') {
                            ?>
                                    <i class="fa-solid fa-circle"
                                        style="color: rgb(255, 123, 0);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                    </i>
                                    <i class="fa-solid fa-circle"
                                        style="color: white;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                    </i>
                            <?php
                        } else if ($color == 'green+white') {
                            ?>
                                        <i class="fa-solid fa-circle"
                                            style="color: green;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                        </i>
                                        <i class="fa-solid fa-circle"
                                            style="color: white;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                        </i>
                            <?php
                        }
                        ?>

                        <!-- <i class="fa-solid fa-circle"
                        style="color: rgb(255, 255, 255);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                    </i> -->
                    </td>
                </tr>
                <?php
            }
            ?>
        </tbody>
    </table>
</div>


<div class="pagination">
    <!-- Previous button -->
    <button class="page_btn" <?php echo $currentPage <= 1 ? 'disabled' : '' ?>
        onclick="get_game_result(<?php echo $currentPage - 1; ?>);">
        <svg xmlns="http://www.w3.org/2000/svg" class="btn--icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
        </svg>
    </button>

    <div class="pages">
        <!-- First page -->
        <?php if ($currentPage > 1): ?>
            <a class="page" href="javascript:void(0);" onclick="get_game_result(1);">1</a>
        <?php endif; ?>

        <!-- Ellipsis if there are pages between first page and current page - 1 -->
        <?php if ($currentPage > 3): ?>
            <span>...</span>
        <?php endif; ?>

        <!-- Display pages on left side of current page -->
        <?php for ($i = max(2, $currentPage - 1); $i < $currentPage; $i++): ?>
            <a class="page" href="javascript:void(0);" onclick="get_game_result(<?php echo $i; ?>);"><?php echo $i; ?></a>
        <?php endfor; ?>

        <!-- Current page -->
        <span class="page active"><?php echo $currentPage; ?></span>

        <!-- Display pages on right side of current page -->
        <?php for ($i = $currentPage + 1; $i <= min($currentPage + 1, $totalPages - 1); $i++): ?>
            <a class="page" href="javascript:void(0);" onclick="get_game_result(<?php echo $i; ?>);"><?php echo $i; ?></a>
        <?php endfor; ?>

        <!-- Ellipsis if there are pages between current page + 1 and last page - 1 -->
        <?php if ($currentPage < $totalPages - 2): ?>
            <span>...</span>
        <?php endif; ?>

        <!-- Last page -->
        <?php if ($totalPages > 1 && $totalPages != $currentPage): ?>
            <a class="page" href="javascript:void(0);"
                onclick="get_game_result(<?php echo $totalPages; ?>);"><?php echo $totalPages; ?></a>
        <?php endif; ?>
    </div>

    <!-- Next button -->
    <button class="page_btn" <?php echo $currentPage >= $totalPages ? 'disabled' : '' ?>
        onclick="get_game_result(<?php echo $currentPage + 1; ?>);">
        <svg xmlns="http://www.w3.org/2000/svg" class="btn--icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
        </svg>
    </button>
</div>



<?php

// echo $totalPages;
// echo $totalRows;