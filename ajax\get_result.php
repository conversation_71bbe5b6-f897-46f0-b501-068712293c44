<?php
session_start();
if ($_SESSION['user_id'] == "") {
    header("location:login.php");
    exit();
}

include ("../admin/lib/db_connection.php");

$user_id = $_SESSION['user_id'];
$game_type = $_POST['game_type'];

date_default_timezone_set("Asia/Kolkata");
$today = date('Y-m-d');
// if ($category == 'parity') { 

?>

<div class="container mt-5">
    <div class="row">
        <div class="col links" style="display: flex; justify-content: center; align-items: center; gap: 20px;">
            <button class="btn btn-primary" id="section1-btn" onclick="showSection('custom-section1')">Game
                History</button>
            <button class="btn btn-primary" id="section2-btn" onclick="showSection('custom-section2')">Chart</button>
            <button class="btn btn-primary" id="section3-btn" onclick="showSection('custom-section3')">My
                history</button>
        </div>
    </div>
    <div class="row">
        <div class="col" style="text-align: center; justify-content: center; align-items: center;">
            <div class="custom-section active" id="custom-section1">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="background-color: #0a58ca; color: #fff;">Period</th>
                                <th style="background-color: #0a58ca; color: #fff;">Number</th>
                                <th style="background-color: #0a58ca; color: #fff;">Big Small</th>
                                <th style="background-color: #0a58ca; color: #fff;">Color</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php

                            // $parityrecordQuery = dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' AND DATE(`date`)='$today' order by id desc limit 30");
                            $parityrecordQuery = dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' AND DATE(`date`)='$today' order by id desc LIMIT 50");
                            $parityrecordRow = dbNumRows($parityrecordQuery);
                            if (!$parityrecordRow) { ?>
                                <tr>
                                    <td colspan="4">
                                        No data available !
                                    </td>
                                </tr>
                                <?php
                            } else {
                                while ($parityResult = dbFetchAssoc($parityrecordQuery)) {

                                    $result = $parityResult['result'];
                                    $color = $parityResult['color'];

                                    ?>
                                    <tr>
                                        <!-- <td><?php echo substr($parityResult['game_id'], 8); ?></td> -->
                                        <td>
                                            <?php echo $parityResult['game_id']; ?>
                                        </td>
                                        <td>
                                            <?php echo $parityResult['result']; ?>
                                        </td>
                                        <td>
                                            <?php echo $parityResult['num_type']; ?>
                                        </td>
                                        <td>
                                            <?php
                                            if ($color == 'orange') {

                                                ?>
                                                <i class="fa-solid fa-circle"
                                                    style="color: rgb(255, 123, 0);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                                </i>
                                                <?php
                                            } else if ($color == 'green') {
                                                ?>
                                                    <i class="fa-solid fa-circle"
                                                        style="color: green;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                                    </i>
                                                <?php
                                            } else if ($color == 'orange+white') {
                                                ?>
                                                        <i class="fa-solid fa-circle"
                                                            style="color: rgb(255, 123, 0);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                                        </i>
                                                        <i class="fa-solid fa-circle"
                                                            style="color: white;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                                        </i>
                                                <?php
                                            } else if ($color == 'green+white') {
                                                ?>
                                                            <i class="fa-solid fa-circle"
                                                                style="color: green;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                                            </i>
                                                            <i class="fa-solid fa-circle"
                                                                style="color: white;box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                                            </i>
                                                <?php
                                            }
                                            ?>

                                            <!-- <i class="fa-solid fa-circle"
                                                style="color: rgb(255, 255, 255);box-shadow: 0px 0px 5px gray; border-radius: 50%;">
                                            </i> -->
                                        </td>
                                    </tr>
                                    <?php
                                }
                            }

                            ?>


                            <!-- <tr>
                                <td>20240323010695</td>
                                <td>2</td>
                                <td>Small</td>
                                <td><i class="fa-solid fa-circle" style="color: rgb(255, 123, 0);"></i>
                                    &nbsp; </td>
                            </tr>
                            <tr>
                                <td>20240323010695</td>
                                <td>3</td>
                                <td>Big</td>
                                <td><i class="fa-solid fa-circle" style="color: rgb(255, 123, 0);"></i>
                                    &nbsp; </td>
                            </tr>
                            <tr>
                                <td>20240323010695</td>
                                <td>4</td>
                                <td>Small</td>
                                <td><i class="fa-solid fa-circle" style="color: green;"></i> &nbsp; </td>
                            </tr>
                            <tr>
                                <td>20240323010695</td>
                                <td>3</td>
                                <td>Big</td>
                                <td><i class="fa-solid fa-circle" style="color: rgb(255, 123, 0);"></i>
                                    &nbsp; </td>
                            </tr>
                            <tr>
                                <td>20240323010695</td>
                                <td>3</td>
                                <td>Big</td>
                                <td><i class="fa-solid fa-circle" style="color: rgb(255, 123, 0);"></i>
                                    &nbsp; </td>
                            </tr> -->
                        </tbody>
                    </table>
                </div>


            </div>
            <div class="custom-section" id="custom-section2">

                <div class="table-container">
                    <table class="table p-2">
                        <thead>
                            <tr>
                                <th style="background-color: #0a58ca; color: #fff;">Period</th>
                                <th colspan="10" style="background-color: #0a58ca; color: #fff;">Number</th>

                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-none">
                                <td colspan="11" style="text-align: left;">
                                    <h3 class="mb-1">Static (last 100 period)</h3>
                                </td>
                            </tr>
                            <tr class="border-none">
                                <td style="text-align: left;width: auto">
                                    <h3 class="mb-0">winning number</h3>
                                </td>
                                <?php
                                for ($i = 0; $i < 10; $i++) { ?>
                                    <td class="m-0 px-1">
                                        <p class="mb-0" style="color: red;font-size: 15px;font-weight: 500;min-width: 15px">
                                            <?php echo $i; ?>
                                        </p>
                                    </td>
                                    <?php
                                }
                                ?>
                            </tr>

                            <?php
                            // Database query to retrieve the last 100 results
                            $query = "SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' ORDER BY id DESC LIMIT 100";
                            $results = dbQuery($query);

                            // Initialize arrays to store frequency and maximum consecutive occurrences
                            $frequency = array_fill(0, 10, 0); // Initialize frequency array with 10 elements, representing numbers 0 to 9
                            $maxConsecutive = array_fill(0, 10, 0); // Initialize max consecutive array with 10 elements, representing numbers 0 to 9
                            
                            // Iterate through each result
                            while ($result = dbFetchAssoc($results)) {
                                // Extract winning numbers from the result
                                $winningNumbers = explode(' ', $result['result']);

                                // Iterate through each winning number
                                $consecutive = 0; // Counter for consecutive occurrences
                                foreach ($winningNumbers as $number) {
                                    $number = intval($number);

                                    // Update frequency
                                    $frequency[$number]++;

                                    // Update max consecutive
                                    $consecutive++;
                                    $maxConsecutive[$number] = max($maxConsecutive[$number], $consecutive);
                                }
                            }

                            // Output frequency
                            // echo "Frequency:<br>";
                            // for ($i = 0; $i < 10; $i++) {
                            //     echo "$i: {$frequency[$i]}<br>";
                            // }
                            
                            // Output max consecutive
                            // echo "<br>Max consecutive:<br>";
                            // for ($i = 0; $i < 10; $i++) {
                            //     echo "$i: {$maxConsecutive[$i]}<br>";
                            // }
                            
                            ?>
                            <tr class="border-none">
                                <td style="text-align: left;">
                                    <h3 class="mb-0">Frequency</h3>
                                </td>
                                <?php
                                for ($i = 0; $i < 10; $i++) {
                                    ?>
                                    <td class="m-0 px-1">
                                        <p class="mb-0"
                                            style="color: gray;font-size: 15px;font-weight: 500;min-width: 15px">
                                            <?php echo $frequency[$i]; ?>
                                        </p>
                                    </td>
                                    <?php
                                }
                                ?>
                            </tr>
                            <tr>
                                <td style="text-align: left;">
                                    <h3 class="mb-0">Max consecutive</h3>
                                </td>
                                <?php
                                for ($i = 0; $i < 10; $i++) {
                                    ?>
                                    <td class="m-0 px-1">
                                        <p class="mb-0"
                                            style="color: gray;font-size: 15px;font-weight: 500;min-width: 15px">
                                            <?php echo $maxConsecutive[$i]; ?>
                                        </p>
                                    </td>
                                    <?php
                                }
                                ?>
                            </tr>

                        </tbody>
                    </table>


                    <table class="table">

                        <tbody>

                            <?php

                            $parityrecordQuery = dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' order by id desc limit 100");
                            $parityrecordRow = dbNumRows($parityrecordQuery);
                            if (!$parityrecordRow) { ?>


                                <tr>
                                    <td colspan="4">
                                        No data available !
                                    </td>
                                </tr>

                                <?php
                            } else {
                                while ($parityResult = dbFetchAssoc($parityrecordQuery)) {

                                    $game_id = $parityResult['game_id'];
                                    $result = $parityResult['result'];
                                    $num_type = $parityResult['num_type'];

                                    if ($parityResult['color'] == "orange+white") {
                                        $color = "orange-white";
                                    } else if ($parityResult['color'] == "green+white") {
                                        $color = "green-white";
                                    } else {
                                        $color = $parityResult['color'];
                                    }


                                    ?>

                                    <tr>
                                        <td style="text-align: left; width: 20%;">
                                            <h3 class="mb-1">
                                                <?php echo $game_id; ?>
                                            </h3>

                                        </td>
                                        <td>
                                            <?php
                                            for ($i = 0; $i <= 9; $i++) {
                                                ?>
                                                <div class="circle <?php echo $i == $result ? $color : '' ?>"><span>
                                                        <?php echo $i; ?>
                                                    </span></div>
                                                <?php
                                            }
                                            ?>
                                            <div class="circle <?php echo $num_type; ?>"><span>
                                                    <?php echo $num_type == 'big' ? 'B' : 'S'; ?>
                                                </span></div>
                                        </td>

                                    </tr>

                                    <?php
                                }
                            }
                            ?>

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="custom-section" id="custom-section3">

                <div class="text-end mt-2">
                    <a href="./bet.php"
                        style="padding: 3px 19px;border: 1px solid grey;font-size: 16px;border-radius: 6px;color: grey;">
                        Details
                    </a>
                </div>
                <div class="mt-4">
                    <div class="accordion" id="accordionExample">
                        <?php

                        // $parityrecordQuery = dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_type`='$game_type' AND DATE(`date`)='$today' order by id desc limit 30");
                        $sel_wingonew_betting = dbQuery("SELECT * FROM `tabl_wingonew_betting` WHERE `game_type`='$game_type' AND user_id='$user_id' AND DATE(`date`)='$today' order by id desc");
                        $num_wingonew_betting = dbNumRows($sel_wingonew_betting);
                        if (!$num_wingonew_betting) { ?>


                            <tr>
                                <td colspan="4">
                                    No data available !
                                </td>
                            </tr>


                            <?php
                        } else {
                            while ($res_wingonew_betting = dbFetchAssoc($sel_wingonew_betting)) {

                                $bet_id = $res_wingonew_betting['id'];
                                $game_id = $res_wingonew_betting['game_id'];
                                $value = $res_wingonew_betting['value'];
                                $bet_date = $res_wingonew_betting['date'];

                                if ($value == "big") {
                                    $color = 'big-b';
                                } else if ($value == "small") {
                                    $color = "small-s";
                                } else if ($value == "green") {
                                    $color = "green-c";
                                } else if ($value == "orange") {
                                    $color = "orange-c";
                                } else if ($value == "white") {
                                    $color = "white-c";
                                } else {
                                    $color = "number-n";
                                }


                                $sel_userresult = dbQuery("SELECT * FROM `tabl_wingonew_userresult` WHERE bet_id='$bet_id'");
                                if ($res_userresult = dbFetchAssoc($sel_userresult)) {
                                    $sel_game_result = dbQuery("SELECT * FROM `tabl_wingonew_result` WHERE `game_id`='$game_id' AND `game_type`='$game_type'");
                                    if ($res_game_result = dbFetchAssoc($sel_game_result)) {

                                        if ($res_game_result['color'] == "orange+white") {
                                            $win_color = "orange white";
                                        } else if ($res_game_result['color'] == "green+white") {
                                            $win_color = "green white";
                                        } else {
                                            $win_color = $res_game_result['color'];
                                        }

                                        $result = ucfirst($res_game_result['result'] . ' ' . $win_color . ' ' . $res_game_result['num_type']);
                                    } else {
                                        $result = 'Waiting';
                                    }

                                    if ($res_userresult['status'] == 'success') {
                                        $result_text = 'Success';
                                        $result_color = 'text-success';
                                        $result_border = 'border: 1px solid #198754';
                                        $win_lose = '+₹ ' . $res_userresult['paidamount'];
                                    } else {
                                        $result_text = 'Failed';
                                        $result_color = 'text-danger';
                                        $result_border = 'border: 1px solid #dc3545';
                                        $win_lose = '-₹ ' . $res_userresult['paidamount'];
                                    }


                                } else {
                                    $result = 'Waiting';
                                    // $result_text = 'Pending';
                                    $result_text = 'Waiting';
                                    $result_color = 'text-warning';
                                    $result_border = 'border: 1px solid #ffc107';
                                    $win_lose = 'Waiting';
                                }

                                ?>

                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button p-0 collapsed" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse_<?php echo $bet_id; ?>" aria-expanded="false"
                                            aria-controls="collapse_<?php echo $bet_id; ?>">
                                            <div class="d-flex justify-content-between m-2 w-100">
                                                <div class="left">
                                                    <div class="d-flex" style="text-align: left; margin-top: 6px">
                                                        <div class="<?php echo $color; ?>">
                                                            <?php echo ucfirst($value); ?>
                                                        </div>
                                                        <div>
                                                            <h3 class="mb-1">
                                                                <?php echo $game_id; ?>
                                                            </h3>
                                                            <p class="mb-0" style="color: grey; font-size: 12px">
                                                                <?php echo $bet_date; ?>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="right pt-2">
                                                    <a href="" class="<?php echo $result_color; ?>"
                                                        style="<?php echo $result_border; ?>;padding: 1px 12px;border-radius: 5px;">
                                                        <?php echo $result_text; ?>
                                                    </a>
                                                    <p style="color: <?php echo $result_color; ?>">
                                                        <?php echo $win_lose; ?>
                                                    </p>
                                                </div>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="collapse_<?php echo $bet_id; ?>" class="accordion-collapse collapse"
                                        aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                        <div class="accordion-body p-3">
                                            <div class="Details">
                                                <h4 style="text-align: justify">Details</h4>
                                            </div>
                                            <!-- <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Order number</p>
                                                <p class="mb-0">dffr355xdfnnnndfsw4yu764</p>
                                            </div> -->
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Game ID</p>
                                                <p class="mb-0">
                                                    <?php echo $game_id; ?>
                                                </p>
                                            </div>
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Purchase Amount</p>
                                                <p class="mb-0">₹
                                                    <?php echo $res_wingonew_betting['amount']; ?>
                                                </p>
                                            </div>

                                            <!-- <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Quantity</p>
                                                <p class="mb-0">10</p>
                                            </div> -->

                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Amount after tax</p>
                                                <p class="mb-0 <?php echo $result_color; ?>">₹
                                                    <?php echo $res_wingonew_betting['amount'] - $res_wingonew_betting['amount'] * (3 / 100); ?>
                                                </p>
                                            </div>
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Tax</p>
                                                <p class="mb-0">₹
                                                    <?php echo $res_wingonew_betting['amount'] * (3 / 100); ?>
                                                </p>
                                            </div>
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Result</p>
                                                <p class="mb-0 <?php echo $result_color; ?>">
                                                    <?php echo $result; ?>
                                                </p>
                                                <!-- <p class="mb-0">5 Red Green Big</p> -->
                                            </div>
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Select</p>
                                                <p class="mb-0 <?php echo $result_color; ?>">
                                                    <?php echo ucfirst($value); ?>
                                                </p>
                                            </div>
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Status</p>
                                                <p class="mb-0 <?php echo $result_color; ?>">
                                                    <?php echo $result_text; ?>
                                                </p>
                                            </div>
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Win/Lose</p>
                                                <p class="mb-0 <?php echo $result_color; ?>">
                                                    <?php echo $win_lose; ?>
                                                </p>
                                            </div>
                                            <div class="order d-flex justify-content-between mb-2"
                                                style="background: #f2f4f7;font-size: 14px;padding: 5px;">
                                                <p class="mb-0">Order Time</p>
                                                <p class="mb-0">
                                                    <?php echo $bet_date; ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php

                            }
                        }
                        ?>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>



<style>
    .border-none {
        border-color: transparent;
        border-style: none;
        border-width: 0px;
    }

    .border-none td {
        padding: 2px 10px;
    }


    .circle {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        /* background-color: #CCCCCC; */

        /* Gray color */
        display: inline-flex;
        justify-content: center;
        align-items: center;
        margin-right: 0px;
        /* Adjust margin as needed */
        /* color: #FFFFFF; */

        border: solid gray 1px;
    }

    .circle span {
        /* Gray color */
        display: inline-flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        /* Gradient background for the circle */
        color: gray;
        font-size: 11px;
    }

    .orange-white {
        background-image: linear-gradient(135deg, #ff5507 50%, white 50%);
    }

    .orange-white span {
        background-image: linear-gradient(135deg, white 50%, #ff5507 50%);
        color: transparent;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: bold;
    }

    .green-white {
        background-image: linear-gradient(135deg, #19752b 50%, white 50%);
    }

    .green-white span {
        background-image: linear-gradient(135deg, white 50%, #19752b 50%);
        color: transparent;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: bold;
    }


    .orange {
        border: none;
        background-color: #ff5507;

    }

    .orange span {
        color: white;
    }

    .green {
        border: none;
        font-size: 11px;
        background-color: #19752b;
    }

    .green span {
        color: white;
    }


    td .big {
        border: none;
        margin-left: 5px;
        background-color: #F3BD14;
    }

    td .big span {
        color: white;
    }

    td .small {
        border: none;
        margin-left: 5px;
        background-color: #47BA7C;
    }

    td .small span {
        color: white;
    }


    .big-b {
        /* background-color: #cdb30b; */
        background-color: #F3BD14;
        color: #fff;
        width: 60px !important;
        height: 47px;
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        border-radius: 13px;
    }


    .small-s {
        /* background-color: #497dec; */

        background-color: #47BA7C;
        color: #fff;
        width: 60px !important;
        height: 47px;
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        border-radius: 13px;
    }



    .green-c {
        background-color: #19752b;
        color: #fff;
        width: 60px;
        height: 47px;
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        border-radius: 13px;
    }

    .orange-c {
        background-color: #ff5507;
        color: #fff;
        width: 60px;
        height: 47px;
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        border-radius: 13px;
    }

    .white-c {
        background-color: #497dec;
        color: #fff;
        width: 60px;
        height: 47px;
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        border-radius: 13px;
    }


    .number-n {
        background-color: #497dec;
        color: #fff;
        width: 60px;
        height: 47px;
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        border-radius: 13px;
    }
</style>