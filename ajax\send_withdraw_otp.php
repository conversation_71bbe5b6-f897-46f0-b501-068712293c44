<?php
@session_start();
include('../admin/lib/db_connection.php');
include('../admin/lib/get_functions.php');
include('../admin/inc/resize-class.php');

if (@$_SESSION['user_id'] != "") {
    // include("../lib/auth.php");

    $userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
    $user = dbFetchAssoc($userq);


    if (isset($user['email'])) {
        $email = $user['email'];
        $name = $user['name'];
        $otp = mt_rand(100000, 999999);
        $_SESSION['otp'] = $otp;

        $to = $email; //'<EMAIL>';
        // $subject = "Air Predict Registration";
        $subject = "Air Predict Withdrawal OTP";

        // $message     = "Thank you for joining us, We hope that you will happy with be our service.";

        $message = "Thank you for using our service. To proceed with your withdrawal, please use the OTP sent to your registered contact information for verification.";
        $message2 = "Your OTP is " . $otp;

        include("../mail.php");
        echo sendmail($to, $subject, $message, $message2, $name);
    } else {
        echo 0;
    }
} else {
    echo 2;
}
