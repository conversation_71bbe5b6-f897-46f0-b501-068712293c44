<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 7;
$sub_page = 70;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Game Result Setting
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">

    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">

    <style>
        .update {
            cursor: pointer;
            color: #515365;
        }
    </style>
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Game Result Setting</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

                                    <div class="widget-content widget-content-area br-6">
                                        <h4>Game Result Setting</h4>
                                        <!-- <a href="add_plans.php"><button class="btn btn-primary mb-2">Add New</button></a> -->
                                        <div class="table-responsive mb-4 mt-4">
                                            <table id="zero-config" class="table table-hover" style="width:100%">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <!-- <th>Date</th> -->
                                                        <th>Name</th>
                                                        <th>Max Loss</th>
                                                        <th>Profit Setting</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    // 'wingo' => 'Wingo',

                                                    $games = array(
                                                        'wingo_cp' => 'Wingo CP',
                                                        'wingo' => 'Colour Prediction',
                                                        'k3' => 'K3',
                                                        '5d' => '5D',
                                                        'trx_win_go' => 'Trx Win Go',
                                                        'parity' => 'Parity',
                                                        'anb' => 'Andar Bahar',
                                                        'wheel' => 'Wheelocity',
                                                        'aviator' => 'aviator',
                                                    );

                                                    $sel = dbQuery("SELECT * FROM tabl_gamesettings WHERE `status`='1' ORDER BY id");
                                                    $i = 1;
                                                    while ($res = dbFetchAssoc($sel)) {
                                                        if ($games[$res['game']] != 'aviator') {

                                                            ?>
                                                            <tr>
                                                                <td>
                                                                    <!-- <?php echo $res['id']; ?> -->
                                                                    <?php echo $i; ?>
                                                                </td>
                                                                <!-- <td>
                                                                <?php echo $res['date']; ?>
                                                            </td> -->
                                                                <td>
                                                                    <?php echo $games[$res['game']]; ?>
                                                                </td>

                                                                <!-- <td class="d-flex" style="width: 150px">
                                                                <input class="form-control" type="number" name="balance"
                                                                    id="game_setting_<?php echo $res['id']; ?>"
                                                                    value="<?php echo $res['max_loss']; ?>"
                                                                    style="width: 80px;padding-right: 10px">

                                                                <span class="update p-2"
                                                                    onClick="update_max_loss(<?php echo $res['id']; ?>, document.getElementById('game_setting_<?php echo $res['id']; ?>').value);">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="30"
                                                                        height="30" viewBox="0 0 24 24" fill="none"
                                                                        stroke="currentColor" stroke-width="2"
                                                                        stroke-linecap="round" stroke-linejoin="round"
                                                                        class="feather feather-edit">
                                                                        <path
                                                                            d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7">
                                                                        </path>
                                                                        <path
                                                                            d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z">
                                                                        </path>
                                                                    </svg>
                                                                </span>
                                                            </td> -->


                                                                <td>
                                                                    <?php
                                                                    $max_losses = array('1', '2');
                                                                    ?>
                                                                    <select class="form-control" name="" id=""
                                                                        onchange="update_max_loss(<?php echo $res['id']; ?>, this.value);">
                                                                        <?php
                                                                        foreach ($max_losses as $max_loss) {
                                                                            $selected = $max_loss == $res['max_loss'] ? "selected" : "";
                                                                            echo "<option value='$max_loss' $selected>" . ucfirst($max_loss) . "</option>";
                                                                        }
                                                                        ?>
                                                                    </select>
                                                                </td>

                                                                <td>
                                                                    <?php
                                                                    $game_settings = array('high', 'low', 'random');
                                                                    ?>
                                                                    <select class="form-control" name="" id=""
                                                                        onchange="update_game_setting(<?php echo $res['id']; ?>, this.value);">
                                                                        <?php
                                                                        foreach ($game_settings as $game_setting) {
                                                                            $selected = $game_setting == $res['settingtype'] ? "selected" : "";
                                                                            echo "<option value='$game_setting' $selected>" . ucfirst($game_setting) . "</option>";
                                                                        }
                                                                        ?>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <?php
                                                        } else {
                                                            ?>
                                                            <tr>
                                                                <td>
                                                                    <?php echo $i; ?>
                                                                </td>
                                                                <td>
                                                                    Aviator
                                                                </td>
                                                                <td>
                                                                    <?php
                                                                    $max_losses = array(1.9, 2.9, 3.9, 4.9, 5.9, 6.9, 7.9, 8.9, 9.9);
                                                                    ?>
                                                                    <select class="form-control" name="" id=""
                                                                        onchange="update_max_loss(<?php echo $res['id']; ?>, this.value);">
                                                                        <?php
                                                                        foreach ($max_losses as $max_loss) {
                                                                            $selected = $max_loss == $res['max_loss'] ? "selected" : "";
                                                                            echo "<option value='$max_loss' $selected>" . ucfirst($max_loss) . "</option>";
                                                                        }
                                                                        ?>
                                                                    </select>
                                                                </td>

                                                                <td>
                                                                    <?php
                                                                    $game_settings = array(1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9);
                                                                    ?>
                                                                    <select class="form-control" name="" id=""
                                                                        onchange="update_game_setting(<?php echo $res['id']; ?>, this.value);">
                                                                        <?php
                                                                        foreach ($game_settings as $game_setting) {
                                                                            $selected = $game_setting == $res['settingtype'] ? "selected" : "";
                                                                            echo "<option value='$game_setting' $selected>" . ucfirst($game_setting) . "</option>";
                                                                        }
                                                                        ?>
                                                                    </select>
                                                                </td>

                                                            </tr>
                                                            <?php
                                                        }
                                                        $i++;
                                                    }
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>



    <!-- END MAIN CONTAINER -->
    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                    "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [8, 10, 20, 50],
            "pageLength": 10
        });
    </script>
    <!-- END PAGE LEVEL SCRIPTS -->
    <script>
        function delete_customer(tabl, id) {
            var retVal = confirm("Are you sure want to delete.");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/delete.php',
                    type: 'post',
                    data: {
                        'tabl': tabl,
                        'row_id': id
                    },
                    success: function (data) {
                        //alert(data);
                        if (data == 1) {
                            location.reload();
                        }
                    },
                });
            } else {
                return false;
            }
        }
    </script>
    <script>
        function change_status(tabl, val, row_id) {
            var retVal = confirm("Are you sure want to activate this course.");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/activate.php',
                    type: 'post',
                    data: {
                        'tabl': tabl,
                        'val': val,
                        'row_id': row_id
                    },
                    success: function (data) {
                        //alert(data);
                        if (data == 1) {
                            location.reload();
                        }
                    },
                });
            } else {
                return false;
            }


        }

        function update_max_loss(game_setting_id, max_loss) {
            var retVal = confirm("Are you sure want to change max loss?");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/update_max_loss.php',
                    type: 'post',
                    data: {
                        'game_setting_id': game_setting_id,
                        'max_loss': max_loss
                    },
                    success: function (data) {
                        //alert(data);
                        if (data == 1) {
                            alert("Max loss update Successfull!");
                            location.reload();
                        } else {
                            alert("Something Went Wrong!");
                        }
                    },
                });
            } else {
                return false;
            }

        }

        function update_game_setting(game_setting_id, settingtype) {
            var retVal = confirm("Are you sure want to change max loss?");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/update_game_setting.php',
                    type: 'post',
                    data: {
                        'game_setting_id': game_setting_id,
                        'settingtype': settingtype
                    },
                    success: function (data) {
                        //alert(data);
                        if (data == 1) {
                            alert("Game setting update Successfull!");
                            location.reload();
                        } else {
                            alert("Something Went Wrong!");
                        }
                    },
                });
            } else {
                return false;
            }

        }

    </script>

    <script>
        function set_homepage(tabl, val, row_id) {
            $.ajax({
                url: 'ajax/global_show.php',
                type: 'post',
                data: {
                    'tabl': tabl,
                    'val': val,
                    'row_id': row_id
                },
                success: function (data) { },
            });
        }
    </script>

</body>

</html>