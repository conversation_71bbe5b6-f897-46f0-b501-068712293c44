<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 3;
$sub_page = 30;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

if (isset($_REQUEST['property_id'])) {
    $id = $_REQUEST['property_id'];
} else {
    echo '<script>window.location.href="business_enquiry.php"</script>';
}

$sel = dbQuery("SELECT * FROM tabl_property WHERE id='" . $id . "'");
$res = dbFetchAssoc($sel);

$sel_location = dbQuery("SELECT * FROM tabl_locations WHERE id='" . $res['location_id'] . "'");
$location = dbFetchAssoc($sel_location);

$sel_pro_type = dbQuery("SELECT * FROM tabl_property_type WHERE id='" . $res['type'] . "'");
$res_pro_type = dbFetchAssoc($sel_pro_type);

// $country = dbQuery("SELECT * FROM tabl_country WHERE iso='" . $res['country'] . "'");
// $res_country = dbFetchAssoc($country);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - View Property </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <style>
        .heading {
            font-size: 20px;
        }

        .data {
            font-size: 20px;
        }

        /* table,
        th,
        td {
            border: 1px solid black;
            border-collapse: collapse;
        } */

        th,
        td {
            padding: 10px;
            text-align: left;
            text-transform: capitalize;
            border: 1px solid black;
        }

        th,
        .td {
            min-width: 15%
        }
    </style>
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">View Property</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing ">
                                    <div class="info section general-info">

                                        <div class="row">
                                            <div class="col-lg-11 mx-auto mb-4">
                                                <h5 class="mt-4 mb-4">View Property</h5>
                                                <table style="width:100%">
                                                    <tr>
                                                        <th>Property For:</th>
                                                        <td class="td" style="text-transform: uppercase"><?php echo $res['property_for']; ?></td>
                                                        <th>Type:</th>
                                                        <td colspan="3" class="td" style="text-transform: uppercase"><?php echo $res_pro_type['name']; ?></td>
                                                    </tr>

                                                    <tr>
                                                        <th>Title:</th>
                                                        <td style="text-transform: uppercase" colspan="5"><?php echo $res['title']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Description:</th>
                                                        <td colspan="5"><?php echo $res['property_description']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Property ID:</th>
                                                        <td colspan="5"><?php echo $res['property_id']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Status:</th>
                                                        <td><?php echo $res['status']; ?></td>

                                                        <th>Material:</th>
                                                        <td><?php echo $res['material']; ?></td>

                                                        <th>Rooms:</th>
                                                        <td><?php echo $res['rooms']; ?></td>
                                                    </tr>

                                                    <tr>
                                                        <th>Beds:</th>
                                                        <td><?php echo $res['beds']; ?></td>

                                                        <th>Baths:</th>
                                                        <td><?php echo $res['baths']; ?></td>

                                                        <th>Halls:</th>
                                                        <td><?php echo $res['halls']; ?></td>
                                                    </tr>

                                                    <tr>
                                                        <th>Kitchens:</th>
                                                        <td><?php echo $res['kitchens']; ?></td>

                                                        <th>Garages:</th>
                                                        <td><?php echo $res['garages']; ?></td>

                                                        <th>Year Build:</th>
                                                        <td><?php echo $res['build_year']; ?></td>
                                                    </tr>

                                                    <tr>
                                                        <th>Area Unit:</th>
                                                        <td colspan="2"><?php echo $res['area_unit']; ?></td>

                                                        <th>Build Area:</th>
                                                        <td colspan="2"><?php echo $res['build_area']; ?></td>


                                                    </tr>

                                                    <tr>
                                                        <th>Plot Dimensions:</th>
                                                        <td colspan="2"><?php echo $res['plot_dimensions']; ?></td>

                                                        <th>Plot Area:</th>
                                                        <td colspan="2"><?php echo $res['plot_area']; ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Tags:</th>
                                                        <td colspan="5"><?php echo $res['tags']; ?></td>
                                                        <!-- <td colspan="4"></td> -->
                                                    </tr>

                                                    <tr>
                                                        <th>Rate:</th>
                                                        <td colspan="2"><?php echo $res['rate']; ?></td>

                                                        <th>Rate:</th>
                                                        <td colspan="2"><?php echo $res['price']; ?></td>
                                                    </tr>

                                                    <tr>
                                                        <th>Hightlights:</th>
                                                        <td colspan="5">
                                                            <?php

                                                            $hls = array(
                                                                "air_conditioning",
                                                                "barbeque",
                                                                "washer_dryer",
                                                                "gym",
                                                                "laundry",
                                                                "lawn",
                                                                "microwave",
                                                                "refrigerator",
                                                                "stunning_views",
                                                                "dining_room",
                                                                "fireplace",
                                                                "pets_allowed",
                                                                "parking",
                                                                "doorman",
                                                                "central_heating",
                                                                "cleaning_service"
                                                            );
                                                            $hl_names = array(
                                                                "Air Conditioning",
                                                                "Barbeque",
                                                                "Washer/Dryer",
                                                                "Gym",
                                                                "Lawn",
                                                                "Microwave",
                                                                "Refrigerator",
                                                                "Stunning views",
                                                                "Dining Room",
                                                                "Fireplace",
                                                                "Pets Allowed",
                                                                "Laundry",
                                                                "Parking",
                                                                "Doorman",
                                                                "Central Heating",
                                                                "Cleaning Service"
                                                            );
                                                            $i = 0;
                                                            $property = '';

                                                            foreach ($hls as $hl) {

                                                                if ($res[$hl]) {
                                                                    if ($i == 0) {
                                                                        echo $hl_names[$i];
                                                                    } else {
                                                                        echo ', ' . $hl_names[$i];
                                                                    }
                                                                }
                                                                $i++;
                                                            }

                                                            ?>



                                                        </td>
                                                    </tr>


                                                    <tr>
                                                        <th>Date:</th>
                                                        <td colspan="5">
                                                            <?php

                                                            $date = strtotime($res['date_added']);

                                                            echo date("d M Y", $date);

                                                            ?>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <th>Cover Image:</th>
                                                        <td colspan="5">
                                                            <img src='../images/property_image/featured/thumb-100/<?php echo $res['featured_img']; ?>' alt=''>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <th>Gallery Image:</th>
                                                        <td colspan="5">
                                                            <?php

                                                            $gallery_sel = dbQuery("SELECT * FROM tabl_gallery_image WHERE property_id='" . $res['id'] . "'");

                                                            while ($gallery_image = dbFetchAssoc($gallery_sel)) {
                                                            ?>
                                                                <img src='../images/property_image/gallery/thumb-100/<?php echo $gallery_image['image']; ?>' alt=''>
                                                            <?php
                                                            }
                                                            ?>


                                                        </td>
                                                    </tr>






                                                </table>

                                                <!-- <div class="row">
                                                    <div class="col-3">
                                                        <p class="heading">Company Address:</p>
                                                    </div>
                                                    <div class="col-5">
                                                        <p class="data"></p>
                                                    </div>
                                                    <div class="col-2">
                                                        <p class="heading">Company Name:</p>
                                                    </div>
                                                    <div class="col-4">
                                                        <p class="data"><?php echo $res['company']; ?></p>
                                                    </div>

                                                </div> -->
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>