<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 6;
$sub_page = 60;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
$date_time = date('Y-m-d H:i:s');
if (isset($_REQUEST['order_status_id'])) {
	if ($_REQUEST['order_status_id'] == 4) {
		dbQuery("UPDATE tabl_order SET order_status_id='" . $_REQUEST['order_status_id'] . "',deliver_date='" . $date_time . "' WHERE id='" . $_REQUEST['order_id'] . "'");
	} else {
		dbQuery("UPDATE tabl_order SET order_status_id='" . $_REQUEST['order_status_id'] . "',deliver_date='0000-00-00 00:00:00' WHERE id='" . $_REQUEST['order_id'] . "'");
	}
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
	<title><?php echo SITE; ?> - View Orders </title>
	<link rel="icon" type="image/x-icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
	<link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
	<script src="assets/js/loader.js"></script>

	<!-- BEGIN GLOBAL MANDATORY STYLES -->
	<link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
	<link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
	<link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
	<!-- END GLOBAL MANDATORY STYLES -->

	<!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
	<link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
	<link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
	<!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

	<link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
	<link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">

	<link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
	<link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>

<body class="alt-menu sidebar-noneoverflow">

	<?php include('inc/__header.php'); ?>
	<!--  BEGIN MAIN CONTAINER  -->
	<div class="main-container" id="container">

		<div class="overlay"></div>
		<div class="search-overlay"></div>

		<!--  BEGIN TOPBAR  -->
		<?php include('inc/__menu.php'); ?>
		<!--  END TOPBAR  -->

		<!--  BEGIN CONTENT PART  -->
		<div id="content" class="main-content">
			<div class="layout-px-spacing">
				<nav class="breadcrumb-one" aria-label="breadcrumb">
					<ol class="breadcrumb">
						<li class="breadcrumb-item"><a href="home.php">Home</a></li>
						<li class="breadcrumb-item"><a href="orders.php">Orders</a></li>
						<li class="breadcrumb-item active"><a href="javascript:void(0);">View Orders</a></li>

					</ol>
				</nav>
				<div class="account-settings-container layout-top-spacing">

					<div class="account-content">
						<div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
							<div class="row">
								<div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

									<div class="widget-content widget-content-area br-6">
										<form name="update_status" id="update_status" method="post">
											<h4>Orders Details</h4>
											<?php

											$sel = dbQuery("SELECT * FROM tabl_order WHERE id='" . $_REQUEST['order_id'] . "'");
											$res = dbFetchAssoc($sel);

											// $state = dbQuery("SELECT name FROM tabl_state WHERE id='" . $res['state'] . "'");
											// $res_state = dbFetchAssoc($state);
											?>
											<table class="table table-bordered table-striped mb-4" style="width:100%">
												<thead>
													<tr>
														<th colspan="2"><strong>Delivery Address</strong></th>
													</tr>
												</thead>
												<tr>
													<td>Order Status</td>
													<td><select name="order_status_id" class="form-control" onchange="set_status()">
															<option value="1" <?php if ($res['order_status_id'] == 1) {
																					echo 'selected';
																				} else {
																					echo '';
																				} ?>>Completed</option>
															<option value="2" <?php if ($res['order_status_id'] == 2) {
																					echo 'selected';
																				} else {
																					echo '';
																				} ?>>Processed</option>
															<option value="3" <?php if ($res['order_status_id'] == 3) {
																					echo 'selected';
																				} else {
																					echo '';
																				} ?>>Cancel</option>
															<option value="4" <?php if ($res['order_status_id'] == 4) {
																					echo 'selected';
																				} else {
																					echo '';
																				} ?>>Delivered</option>
															<option value="5" <?php if ($res['order_status_id'] == 5) {
																					echo 'selected';
																				} else {
																					echo '';
																				} ?>>Return</option>
															<option value="6" <?php if ($res['order_status_id'] == 6) {
																					echo 'selected';
																				} else {
																					echo '';
																				} ?>>Refunded</option>
															<option value="0" <?php if ($res['order_status_id'] == 0) {
																					echo 'selected';
																				} else {
																					echo '';
																				} ?>>Pending</option>
														</select>
													</td>
												</tr>

												<tr>
													<td>Name</td>
													<td><strong><?php echo  $res['name']; ?></strong></td>
												</tr>
												<tr>
													<td>Email</td>
													<td><strong><?php echo  $res['email']; ?></strong></td>
												</tr>
												<tr>
													<td>Phone</td>
													<td><strong><?php echo  $res['phone']; ?></strong></td>
												</tr>
												<tr>
													<td>Address</td>
													<td><strong><?php echo  $res['address']; ?></strong></td>
												</tr>
												<!-- <tr>
													<td>ZipCode</td>
													<td><strong><?php echo  $res['zipcode']; ?></strong></td>
												</tr> -->
												<!-- <tr>
													<td>Country</td>
													<td><strong><?php echo  $res['country']; ?></strong></td>
												</tr> -->
												<!-- <tr>
													<td>State</td>
													<td><strong><?php echo  $res_state['name']; ?></strong></td>
												</tr> -->
												<!-- <tr>
													<td>City</td>
													<td><strong><?php echo  $res['city']; ?></strong></td>
												</tr> -->
												<tr>
													<td>Payable Amt</td>
													<td><strong><?php echo  $res['total']; ?></strong></td>
												</tr>
												<tr>
													<td>Payment Methods</td>
													<td><strong> <?php if ($res['payment_type'] == 1) {
																		echo 'Online Payment';
																	} elseif ($res['payment_type'] == 0) {
																		echo 'Cash on Delivery';
																	} else {
																		echo 'Wallet';
																	}
																	?></strong></td>
												</tr>
												<tr>
													<td>Transaction No.</td>
													<td><strong><?php echo  $res['transaction_id']; ?></strong></td>
												</tr>
											</table>
											<table class="table table-bordered table-striped mb-4" style="width:100%">
												<thead>
													<tr>
														<th colspan="5"><strong>Product Details</strong></th>
													</tr>
												</thead>
												<tr>
													<td>Image</td>
													<td>Name</td>
													<td>Qty</td>
													<td>Unit Price</td>
													<td>Total Price</td>

												</tr>
												<?php
												$pro_detail = dbQuery("SELECT * FROM tabl_order_product WHERE order_id='" . $_REQUEST['order_id'] . "'");
												$shipping_total = 0;
												$total = 0;
												$grand_total = 0;
												$i = 1;
												while ($res_pro_detail = dbFetchAssoc($pro_detail)) {

													// $color = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_pro_detail['color'] . "'");
													// $color_num = dbNumRows($color);
													// $res_color = dbFetchAssoc($color);

													// $size = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_pro_detail['size'] . "'");
													// $size_num = dbNumRows($size);
													// $res_size = dbFetchAssoc($size);

													$image = dbQuery("SELECT image FROM tabl_products WHERE id='" . $res_pro_detail['product_id'] . "'");
													$res_image = dbFetchAssoc($image);

												?>
													<tr>
														<td>
															<a href="#"><img src="../assets/img/products/thumb-100/<?php echo $res_image['image'] ?>" alt="product" class="img-fluid" width="80"></a>
														</td>
														<!-- <td>
															<strong><?php echo $res_pro_detail['name'] ?></strong>
															<br />
															<?php if ($color_num > 0) { ?>
																<span>Color: <?php echo $res_color['v_value']; ?></span><br />
															<?php } ?>
															<?php if ($size_num > 0) { ?>
																<span>Size: <?php echo $res_size['v_value']; ?></span><br>
															<?php } ?>
														</td> -->
														<td><strong><?php echo $res_pro_detail['name'] ?></strong></td>
														<td><strong><?php echo $res_pro_detail['qty'] ?></strong></td>
														<td><strong><?php echo $res_pro_detail['price'] ?></strong></td>
														<td><strong><?php echo $res_pro_detail['total'] ?></strong></td>
													</tr>

												<?php $total += $res_pro_detail['total'];
												} ?>
												<?php $shipping = dbQuery("SELECT sum(shipping_rate) as ttlshipping FROM `tabl_order_shipping` WHERE order_id='" . $res['id'] . "'");
												$res_shipping = dbFetchAssoc($shipping);
												if ($res_shipping['ttlshipping'] == "") {
													$shipping_rate = 0;
												} else {
													$shipping_rate = $res_shipping['ttlshipping'];
												}

												$promo = dbQuery("SELECT sum(discount) as discount FROM `tabl_order_promo` WHERE order_id='" . $res['id'] . "'");
												$res_promo = dbFetchAssoc($promo);
												if ($res_promo['discount'] == "") {
													$discount = 0;
												} else {
													$discount = $res_promo['discount'];
												}
												?>
												<tr>
													<td></td>
													<td></td>
													<td></td>
													<td><strong>Total</strong></td>
													<!-- <td><strong><?php echo  $total; ?></strong></td> -->
													<td><strong><?php echo  $res['sub_total']; ?></strong></td>
												</tr>
												<tr>
													<td></td>
													<td></td>
													<td></td>
													<td><strong>Tax 5%</strong></td>
													<!-- <td><strong><?php echo  $total; ?></strong></td> -->
													<td><strong><?php echo  $res['tax']; ?></strong></td>
												</tr>
												<tr>
													<td></td>
													<td></td>
													<td></td>
													<td><strong>Delivery</strong></td>
													<!-- <td><strong><?php echo $shipping_rate; ?></strong></td> -->
													<td><strong><?php echo  $res['shipping']; ?></strong></td>
												</tr>
												<tr>
													<td></td>
													<td></td>
													<td></td>
													<td><strong>Discount</strong></td>
													<td><strong>-<?php echo $discount; ?></strong></td>
												</tr>
												<tr>
													<td></td>
													<td></td>
													<td></td>
													<td><strong>Grand Total</strong></td>
													<td><strong><?php echo $res['total'] ?></strong></td>
												</tr>
											</table>
										</form>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<?php include('inc/__footer.php'); ?>
			</div>

		</div>
		<!--  END CONTENT PART  -->

	</div>
	<!-- END MAIN CONTAINER -->
	<!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
	<script src="assets/js/libs/jquery-3.1.1.min.js"></script>
	<script src="bootstrap/js/popper.min.js"></script>
	<script src="bootstrap/js/bootstrap.min.js"></script>
	<script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
	<script src="assets/js/app.js"></script>

	<script>
		$(document).ready(function() {
			App.init();
		});
	</script>
	<script src="assets/js/custom.js"></script>
	<!-- END GLOBAL MANDATORY SCRIPTS -->

	<!-- BEGIN PAGE LEVEL SCRIPTS -->
	<script src="plugins/table/datatable/datatables.js"></script>
	<script>
		$('#zero-config').DataTable({
			"oLanguage": {
				"oPaginate": {
					"sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
					"sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
				},
				"sInfo": "Showing page _PAGE_ of _PAGES_",
				"sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
				"sSearchPlaceholder": "Search...",
				"sLengthMenu": "Results :  _MENU_",
			},
			"stripeClasses": [],
			"lengthMenu": [8, 10, 20, 50],
			"pageLength": 10
		});
	</script>
	<!-- END PAGE LEVEL SCRIPTS -->
	<script>
		function delete_order(id) {
			var retVal = confirm("Are you sure want to delete.");
			if (retVal == true) {
				$.ajax({
					url: 'ajax/delete_order.php',
					type: 'post',
					data: {
						'id': id
					},
					success: function(data) {
						//alert(data);
						if (data == 1) {
							location.reload();
						}
					},
				});
			} else {
				return false;
			}

		}
	</script>
	<script>
		function change_status(tabl, val, row_id) {
			var retVal = confirm("Are you sure want to activate this course.");
			if (retVal == true) {
				$.ajax({
					url: 'ajax/activate_course.php',
					type: 'post',
					data: {
						'tabl': tabl,
						'val': val,
						'row_id': row_id
					},
					success: function(data) {
						//alert(data);
						if (data == 1) {
							location.reload();
						}
					},
				});
			} else {
				return false;
			}


		}
	</script>
	<script>
		function set_status() {
			$("#update_status").submit()
		}
	</script>

</body>

</html>