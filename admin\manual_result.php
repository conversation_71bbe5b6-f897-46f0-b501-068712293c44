<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 6;
$sub_page = 60;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

$id = 1;

if (isset($_REQUEST['submit'])) {

    $value[0]  = mysqli_real_escape_string($con, $_REQUEST['a']);
    $value[1]  = mysqli_real_escape_string($con, $_REQUEST['b']);
    $value[2]  = mysqli_real_escape_string($con, $_REQUEST['c']);

    for ($i = 0; $i < 3; $i++) {

        if ($value[$i] != '') {
            $status[$i] = dbQuery("UPDATE tabl_manualresultswitch SET switch='yes',value='" . $value[$i] . "' WHERE  id='" . ($i + 1) . "'");

            //echo "UPDATE tabl_manualresultswitch SET switch='yes',value='" . $value[$i] . "' WHERE  id='" . ($i + 1) . "'";
        } else {
            $status[$i] = dbQuery("UPDATE tabl_manualresultswitch SET switch='no',value=NULL WHERE  id='" . ($i + 1) . "'");

            //echo "UPDATE tabl_manualresultswitch SET switch='no',value=NULL WHERE  id='" . ($i + 1) . "'";
        }
    }

    if ($status[0] && $status[1] && $status[2]) {
        echo '<script>alert("Manual Result Updated!");window.location.href="manual_result.php"</script>';
    } else {
        echo '<script>alert("Something Went Wrong!");window.location.href="manual_result.php"</script>';
    }
}

$sel = dbQuery("SELECT * FROM tabl_manualresultswitch");
$i = 0;
while ($res = dbFetchAssoc($sel)) {
    $value[$i] = $res['value'];
    $i++;
}

$sel2 = dbQuery("SELECT * FROM tabl_gameid ORDER BY id DESC LIMIT 1");
$res2 = dbFetchAssoc($sel2);

$game_id = $res2['gameid'];


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Manual Result </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">

</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="manual_result.php">Manual Result</a></li>
                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">

                                        <div class="info">
                                            <h4 class="mb-0">Manual Result <span style="float: right;">Counter: <span id="demo"></span></span></h4>
                                            <h6 class="">Game ID: <span id="gameid1"><?php echo $game_id; ?></span></h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">

                                                                    <div class="col-sm-12 col-md-4">
                                                                        <div class="form-group">
                                                                            <label for="fullName">A</label>
                                                                            <input type="text" class="form-control mb-4" id="a" maxlength="1" name="a" value="<?php echo $value[0]; ?>" placeholder="A">

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-4">
                                                                        <div class="form-group">
                                                                            <label for="fullName">B</label>
                                                                            <input type="text" class="form-control mb-4" id="b" maxlength="1" name="b" value="<?php echo $value[1]; ?>" placeholder="B">

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-4">
                                                                        <div class="form-group">
                                                                            <label for="fullName">C</label>
                                                                            <input type="text" class="form-control mb-4" id="c" maxlength="1" name="c" value="<?php echo $value[2]; ?>" placeholder="C">

                                                                        </div>
                                                                    </div>


                                                                    <!--<div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Description</label>
                                                                            <textarea class="form-control mb-4" id="description" name="description" placeholder="Description" rows="5" required><?php echo $res['description']; ?></textarea>
                                                                        </div>
                                                                        
                                                                    </div>-->



                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Update</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

                                    <div class="widget-content widget-content-area br-6">
                                        <h4>Betting Data For <span id="gameid2"><?php echo $game_id; ?></span></h4>
                                        <!-- <a href="add_plans.php"><button class="btn btn-primary mb-2">Add New</button></a> -->
                                        <div class="table-responsive mb-4 mt-4">
                                            <table id="zero-config" class="table table-hover" style="width:100%">
                                                <thead>
                                                    <tr>
                                                        <th>Game ID</th>
                                                        <th>Number</th>
                                                        <th>Betting On A</th>
                                                        <th>Betting On B</th>
                                                        <th>Betting On C</th>

                                                    </tr>
                                                </thead>
                                                <tbody id="betting_data">
                                                    <?php
                                                    for ($i = 0; $i < 10; $i++) {
                                                    ?>
                                                        <tr>
                                                            <td><?php echo $game_id; ?></td>
                                                            <td><?php echo $i; ?></td>
                                                            <td><?php echo bet_coins($game_id, 'a', $i); ?></td>
                                                            <td><?php echo bet_coins($game_id, 'b', $i); ?></td>
                                                            <td><?php echo bet_coins($game_id, 'c', $i); ?></td>

                                                        </tr>
                                                    <?php
                                                    } ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>




                    <input type="hidden" name="gameid" id="gameid" value=''>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function() {
            var x = setInterval(function() {
                start_count_down();

                // $('#closbtnloader').click();
            }, 1e3);

            generateGameid();

        });

        function start_count_down() {
            // $(".showload").hide();
            // $(".none").show();
            var countDownDate = Date.parse(new Date) / 1e3;
            var now = new Date().getTime();
            var distance = 600 - countDownDate % 600;
            //alert(distance);
            var i = distance / 60,
                n = distance % 60,
                o = n / 10,
                s = n % 10;
            var minutes = Math.floor(i);
            var seconds = ('0' + Math.floor(n)).slice(-2);
            document.getElementById("demo").innerHTML = "<span class='timer'>0" + Math.floor(minutes) + "</span>" + "<span>:</span>" + "<span class='timer'>" + seconds + "</span>";
            document.getElementById("counter").value = distance;

            if (distance == 600 || distance == 480 || distance == 360 || distance == 180 || distance == 120 || distance < 120) {
                generateGameid();
            }
        }

        function betting_data(gameid) {
            // alert('hello');
            //var gameid = document.getElementById('gameid').value;
            $.ajax({
                type: "Post",
                data: "gameid=" + gameid,
                url: "./ajax/betting_data.php",
                success: function(html) {
                    document.getElementById("betting_data").innerHTML = html;
                },
                error: function(e) {}
            });
        }

        function generateGameid() {
            $.ajax({
                type: "Post",
                data: "type=" + "generate",
                url: "../ajax_user/gameid-generation.php",
                success: function(html) {
                    //alert(html);
                    var arr = html.split('~');
                    //alert(arr[1]);
                    document.getElementById("gameid").value = arr[0];
                    document.getElementById("gameid1").innerHTML = arr[0];
                    document.getElementById("gameid2").innerHTML = arr[0];

                    //document.getElementById("futureid").value = arr[0];

                    betting_data(arr[0]);

                    return false;
                },
                error: function(e) {}
            });
        }
    </script>

    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                    "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [8, 10, 20, 50],
            "pageLength": 10
        });
    </script>


    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
    CKEDITOR.replace('description');
</script>