<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 5;
$sub_page = 50;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['submit'])) {

    $plan_id        = mysqli_real_escape_string($con, $_REQUEST['plan_id']);
    $title          = mysqli_real_escape_string($con, $_REQUEST['title']);
    $type           = mysqli_real_escape_string($con, $_REQUEST['type']);
    $price          = mysqli_real_escape_string($con, $_REQUEST['price']);
    $duration       = mysqli_real_escape_string($con, $_REQUEST['duration']);

    $limit          = mysqli_real_escape_string($con, $_REQUEST['limit']);

    $showcase       = 0;

    // $showcase       = mysqli_real_escape_string($con, $_REQUEST['showcase']);
    $description    = mysqli_real_escape_string($con, $_REQUEST['description']);


    //dbQuery("UPDATE tabl_plans SET title='" . mysqli_real_escape_string($con, $_REQUEST['title']) . "',author='" . mysqli_real_escape_string($con, $_REQUEST['author']) . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',quotes='" . mysqli_real_escape_string($con, $_REQUEST['quotes']) . "',date_added='" . $_REQUEST['date_added'] . "' WHERE id='" . $_REQUEST['id'] . "'");

    $result = dbQuery("UPDATE tabl_plans SET title='" . $title . "',type='" . $type . "',price='" . $price . "',duration='" . $duration . "',`limit`='" . $limit . "',showcase='" . $showcase . "',description='" . $description . "' WHERE id='" . $plan_id . "'");

    if ($result) {
        echo '<script>alert("Plan Edited!");window.location.href="plans.php"</script>';
    } else {
        echo '<script>alert("Something Went Wrong!");window.location.href="plans.php"</script>';
    }
}

$sel = dbQuery("SELECT * FROM tabl_plans WHERE id='" . $_REQUEST['id'] . "'");
$res = dbFetchAssoc($sel);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Edit Plans </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN THEME GLOBAL STYLES -->
    <link href="plugins/flatpickr/flatpickr.css" rel="stylesheet" type="text/css">
    <link href="plugins/noUiSlider/nouislider.min.css" rel="stylesheet" type="text/css">
    <!-- END THEME GLOBAL STYLES -->

    <!--  BEGIN CUSTOM STYLE FILE  -->

    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/scrollspyNav.css" rel="stylesheet" type="text/css" />
    <link href="plugins/flatpickr/custom-flatpickr.css" rel="stylesheet" type="text/css">
    <link href="plugins/noUiSlider/custom-nouiSlider.css" rel="stylesheet" type="text/css">
    <link href="plugins/bootstrap-range-Slider/bootstrap-slider.css" rel="stylesheet" type="text/css">
    <!--  END CUSTOM STYLE FILE  -->

    <style>
        /* The container */
        .container-check {
            display: block;
            position: relative;
            padding-left: 35px;
            margin-bottom: 12px;
            cursor: pointer;
            font-size: 22px !important;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Hide the browser's default checkbox */
        .container-check input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
            padding: 5px !important
        }

        /* Create a custom checkbox */
        .checkmark {
            position: absolute;
            top: 5px;
            left: 0;
            height: 25px;
            width: 25px;
            background-color: #eee;
        }

        /* On mouse-over, add a grey background color */
        .container-check:hover input~.checkmark {
            background-color: #ccc;
        }

        /* When the checkbox is checked, add a blue background */
        .container-check input:checked~.checkmark {
            background-color: #2196F3;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .container-check input:checked~.checkmark:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .container-check .checkmark:after {
            left: 9px;
            top: 5px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 3px 3px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }
    </style>



</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="plans.php">Plans</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Edit</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Edit Plans</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-12 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">

                                                                <div class="form-row">

                                                                    <div class="form-group col-md-12">
                                                                        <label for="fullName"><strong>Title</strong></label>
                                                                        <input type="text" class="form-control" id="Name" name="title" placeholder="Title" value="<?php echo $res['title'] ?>" required>
                                                                    </div>
                                                                </div>
                                                                <div class="form-row">

                                                                    <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Type</strong></label>
                                                                        <!-- <input type="number" class="form-control" id="type" name="type" placeholder="Type" value="<?php echo $res['type'] ?>" required> -->

                                                                        <select name="type" id="" class="form-control">
                                                                            <option value="">Select</option>

                                                                            <?php

                                                                            $types = array("image", "video");
                                                                            foreach ($types as $type) {
                                                                                if ($type == $res['type']) {
                                                                                    $selected = 'selected';
                                                                                } else {
                                                                                    $selected = '';
                                                                                }
                                                                            ?>
                                                                                <option value="<?php echo $type ?>" <?php echo $selected ?>><?php echo ucfirst($type); ?></option>
                                                                            <?php
                                                                            }

                                                                            ?>

                                                                        </select>
                                                                    </div>

                                                                    <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Price</strong></label>
                                                                        <input type="number" class="form-control" id="price" name="price" placeholder="Price" value="<?php echo $res['price'] ?>" required>
                                                                    </div>

                                                                    <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Duration in Days</strong></label>
                                                                        <input type="text" class="form-control" id="duration" name="duration" value="<?php echo $res['duration'] ?>" placeholder="Duration in Days" required>
                                                                    </div>

                                                                    <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Limit</strong></label>
                                                                        <input type="text" class="form-control" id="limit" name="limit" value="<?php echo $res['limit'] ?>" placeholder="Enter Product Listing Limit" required>
                                                                    </div>

                                                                    <!-- <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Showcase Product Limit</strong></label>
                                                                        <input type="number" class="form-control" id="showcase" name="showcase" value="<?php echo $res['showcase'] ?>" placeholder="Enter Showcase Product Limit" required>
                                                                    </div> -->

                                                                </div>


                                                                <div class="row">


                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"><strong>Full Description</strong></label>
                                                                            <textarea class="form-control" id="description" name="description" placeholder="Description" required rows="5"><?php echo $res['description'] ?></textarea>
                                                                        </div>

                                                                    </div>
                                                                    <input type="hidden" name="plan_id" value="<?php echo $res['id'] ?>">

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Submit</button>
                                                                        </div>

                                                                    </div>



                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="plugins/jquery-ui/jquery-ui.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="plugins/highlight/highlight.pack.js"></script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="assets/js/scrollspyNav.js"></script>
    <script src="plugins/flatpickr/flatpickr.js"></script>
    <script src="plugins/noUiSlider/nouislider.min.js"></script>

    <script src="plugins/flatpickr/custom-flatpickr.js"></script>
    <script src="plugins/noUiSlider/custom-nouiSlider.js"></script>
    <script src="plugins/bootstrap-range-Slider/bootstrap-rangeSlider.js"></script>

    <script>
        function displayResult()

        {

            var i = document.getElementById("num").value;

            document.getElementById("classes").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><select name="class_type_' + i + '" id="class_type_' + i + '" class="form-control"><option value="0">SELECT</option><option value="1">Pre-Recorded</option><option value="2">Free Classes</option></select></td><td><select name="type_' + i + '" id="type_' + i + '" class="form-control" onChange="set_class_type(' + i + ',this.value)"><option value="0">SELECT</option><option value="1">PDF</option><option value="2">URL</option></select></td><td><input type="text" name="file_' + i + '" id="doc_type_' + i + '"  class="form-control" accept="application/pdf"></td></tr>';
            i++;

            var num = document.getElementById("num").value = i;

        }
    </script>

    <script>
        function isNumber(evt) {
            var iKeyCode = (evt.which) ? evt.which : evt.keyCode
            if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
                return false;

            return true;
        }

        function isDecimal(evt, obj) {

            var charCode = (evt.which) ? evt.which : event.keyCode
            var value = obj.value;
            var dotcontains = value.indexOf(".") != -1;
            if (dotcontains)
                if (charCode == 46) return false;
            if (charCode == 46) return true;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
    </script>
    <script>
        function set_sub_cat(cat_id) {
            $.ajax({
                url: 'ajax/get_sub_category.php',
                type: 'post',
                data: {
                    'cat_id': cat_id
                },
                success: function(data) {
                    if (data != "") {
                        $("#sub_cat_id").html(data);
                    } else {
                        $("#sub_cat_id").html('<option value="0">SELECT</option>');

                    }
                },
            });

        }
    </script>
    <script>
        function set_class_type(id, val) {
            if (val == 1) {
                $("#doc_type_" + id).attr('type', 'file');
            } else {
                $("#doc_type_" + id).attr('type', 'text');
            }
        }
    </script>
</body>

</html>