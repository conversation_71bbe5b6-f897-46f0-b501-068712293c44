<?php
include ('../admin/lib/db_connection.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

// Check if user is logged in
if (!isset($_REQUEST['id']) || empty($_REQUEST['id'])) {
    ?>
    <div class="alert bg-white mt-3 mb-1 p-2 text-center">
        <!-- <p class="my-2"><b>Invalid level id!</b></p> -->
        <!-- <p class="my-2">Invalid level id!</p> -->
        <p class="my-2">No experience found!</p>
    </div>
    <?php
    die();
}

$id = $_REQUEST['id'];

$sel_exp = dbQuery("SELECT * FROM tabl_experience WHERE vip_level='$id' ORDER BY id DESC");
$num_exp = dbNumRows($sel_exp);

if ($num_exp) {
    while ($res_exp = dbFetchAssoc($sel_exp)) {
        ?>
        <div class="alert bg-white mt-3 mb-1 p-2 ">
            <div class="login-noti deposit-border">
                <div class="withdraw">

                    <p>Experience Bonus</p>
                </div>

            </div>
            <div class="balance d-flex align-items-center justify-content-between mt-2">
                <p>Betting EXP</p>

            </div>
            <div class="balance d-flex align-items-center justify-content-between mt-2 mb-2">
                <p>
                    <?php echo $res_exp['date']; ?>
                </p>
                <p class="red">
                    <?php echo (int) $res_exp['exp']; ?> EXP
                </p>
            </div>
        </div>

        <?php
    }
} else {
    ?>
    <div class="alert bg-white mt-3 mb-1 p-2 text-center">
        <!-- <p class="my-2"><b>No experience found!</b></p> -->
        <p class="my-2">No experience found!</p>
    </div>
    <?php
    die();
}