<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 10;
$sub_page = 102;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');



$game_type = 1;
if (isset($_REQUEST['game_type']) && $_REQUEST['game_type'] != '') {
    $game_type = $_REQUEST['game_type'];
}

if ($game_type == 1) {
    $game_duration = 1;
    $game_stop = 10;
} else if ($game_type == 2) {
    $game_duration = 3;
    $game_stop = 30;
} else if ($game_type == 3) {
    $game_duration = 5;
    $game_stop = 30;
} else if ($game_type == 4) {
    $game_duration = 10;
    $game_stop = 30;
}

// $game_id = get_wingo_gameid($game_type);

$user_q = '';
if (isset($_REQUEST['user_id']) && $_REQUEST['user_id'] != '') {
    $user_id = $_REQUEST['user_id'];

    $user_q = "WHERE user_id='$user_id'";
}



?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Bet History
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">



    <style>
        .btn-outline-orange {
            color: #ff5507;
            background-color: transparent;
            background-image: none;
            border-color: #ff5507;
            margin-right: 5px;
        }

        .btn-outline-orange:hover {
            background-color: #ff5507;

        }

        .btn-outline-violet {
            color: #6655d3;
            background-color: transparent;
            background-image: none;
            border-color: #6655d3;
            margin-right: 5px;
        }

        .btn-outline-violet:hover {
            background-color: #6655d3;

        }

        .w-100 {
            width: 100%;
        }
    </style>
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="manual_result.php">Bet History</a></li>
                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">


                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">

                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

                                    <div class="widget-content widget-content-area br-6">
                                        <div class="row">
                                            <div class="col-12 col-sm-8">
                                                <h4 class="mb-0">Bet History: Avitor</h4>

                                            </div>


                                        </div>


                                        <!-- <h4>Betting Data For: <span id="gameid2">
                                                <?php echo get_wingo_gameid($game_type); ?>
                                            </span> - Wingo
                                            <?php echo $game_duration; ?> Min
                                        </h4> -->


                                        <!-- <a href="add_plans.php"><button class="btn btn-primary mb-2">Add New</button></a> -->
                                        <div class="table-responsive mb-4 mt-4">
                                            <table id="zero-config" class="table table-hover" style="width:100%">
                                                <thead>
                                                    <tr>
                                                        <th>S. no.</th>
                                                        <th>Datetime</th>
                                                        <th>User Phone</th>
                                                        <th>User Name</th>
                                                        <th>Game ID</th>
                                                        <th>Bet ID</th>
                                                        <th>Bet Amount</th>
                                                        <th>Cashout Multiplier</th>
                                                        <th>Profit/Loss</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="betting_data">
                                                    <?php

                                                    // $game_values = array(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 'green', 'white', 'orange', 'big', 'small');
                                                    
                                                    // $game_values = array(0, 1, 2, 3, 4, 5, 6, 7, 8, 9);
                                                    
                                                    // foreach ($game_values as $game_value) {
                                                    
                                                    $i = 1;

                                                    $sel_aviator_betting = dbQuery("SELECT * FROM `tabl_aviator_betting` $user_q ORDER BY `date` DESC");

                                                    while ($res_aviator_betting = dbFetchAssoc($sel_aviator_betting)) {
                                                        $bet_id = $res_aviator_betting['id'];
                                                        $game_id = $res_aviator_betting['game_id'];

                                                        $user_id = $res_aviator_betting['user_id'];

                                                        $cashout_multiplier = $res_aviator_betting['cashout_multiplier'];
                                                        $amount = $res_aviator_betting['amount'];
                                                        $bet_date = date("Y-m-d", strtotime($res_aviator_betting['date']));
                                                        $status = $res_aviator_betting['status'];

                                                        $status = $res_aviator_betting['status'];

                                                        $user_name = "";
                                                        $user_code = "";
                                                        $user_phone = "";
                                                        $sel_user = dbQuery("SELECT * FROM tabl_user WHERE id='$user_id'");
                                                        if ($res_user = dbFetchAssoc($sel_user)) {
                                                            $user_name = $res_user['name'];
                                                            $user_code = $res_user['own_code'];
                                                            $user_phone = $res_user['phone'];
                                                        }

                                                        if ($status) {

                                                            // if ($res_userresult['status'] == 'success') {
                                                    
                                                            if ($cashout_multiplier > 0) {
                                                                $result_text = 'Win';
                                                                $result_color = 'text-success';
                                                                $result_border = 'border: 1px solid #198754';
                                                                $win_lose = '+₹ ' . $amount * $cashout_multiplier;
                                                                // $win_amt = $res_userresult['paidamount'] - $amount;
                                                                $win_amt = $amount * $cashout_multiplier - $amount;
                                                            } else {
                                                                $result_text = 'Loss';
                                                                $result_color = 'text-danger';
                                                                $result_border = 'border: 1px solid #dc3545';
                                                                // $win_lose = '-₹ ' . $res_userresult['paidamount'];
                                                    
                                                                $win_lose = '+₹ ' . $amount * $cashout_multiplier;
                                                                $win_amt = $amount;
                                                            }

                                                        } else {
                                                            $result = 'Waiting';
                                                            // $result_text = 'Pending';
                                                            $result_text = 'Waiting';
                                                            $result_color = 'text-warning';
                                                            $result_border = 'border: 1px solid #ffc107';
                                                            $win_lose = 'Waiting';
                                                            $win_amt = '0';
                                                            $win_data = array();
                                                        }
                                                        ?>
                                                        <tr>
                                                            <td>
                                                                <?php echo $i; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo date("d-m-Y h:i A", strtotime($res_aviator_betting['date'])); ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $user_phone; ?>
                                                                <!-- <?php echo $user_id; ?> -->
                                                            </td>
                                                            <td>
                                                                <?php echo $user_name; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $game_id; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $bet_id; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $amount; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $cashout_multiplier ?>

                                                            </td>

                                                            <td>
                                                                <h6 class="<?php echo $result_color; ?>">
                                                                    <?php echo $win_lose; ?>
                                                                </h6>
                                                            </td>

                                                            <td>
                                                                <h6 class="<?php echo $result_color; ?>">
                                                                    <?php echo $result_text; ?>
                                                                </h6>
                                                            </td>

                                                        </tr>
                                                        <?php

                                                        $i++;
                                                    }
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>




                    <input type="hidden" name="gameid" id="gameid" value=''>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>

        function change_game_type(game_type) {
            window.location.href = "./wingo_bet_history.php?game_type=" + game_type;
        }


        function get_game_id() {
            var game_id = $('#game_id').val();
            var game_type = <?php echo $game_type; ?>;

            $.ajax({
                type: "Post",
                data: "game_type=" + game_type,
                url: "../ajax/get_wingo_game_id.php",
                success: function (html) {
                    var arr = html.split('~');
                    // document.getElementById("game_id").innerHTML = html;

                    document.getElementById("gameid").value = arr[0];
                    document.getElementById("gameid1").innerHTML = arr[0];
                    document.getElementById("gameid2").innerHTML = arr[0];

                    betting_data(arr[0]);

                    return html;
                },
                error: function (e) {

                }
            });
        }


    </script>

    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                    "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [8, 10, 20, 50],
            "pageLength": 10
        });
    </script>


    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
    CKEDITOR.replace('description');
</script>