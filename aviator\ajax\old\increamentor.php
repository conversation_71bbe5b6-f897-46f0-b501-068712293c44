<?php
ob_start();
session_start();
if ($_SESSION['user_id'] == "") {
    header("location:login.php");
    exit();
}
// echo $_SESSION['user_id'];

include ('../../admin/lib/db_connection.php');
include ('../lib/gamesetting.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');


// Function to send JSON response
function json_response($data, $isSuccess, $message)
{
    $res = array(
        "data" => $data,
        "isSuccess" => $isSuccess,
        "message" => $message
    );
    return json_encode($res);
}

// Function to fetch user details


function increamentor()
{

    // Get game status data
    $sql_game_status = dbQuery("SELECT * FROM tabl_aviator_settings WHERE category = 'game_status' LIMIT 1");
    $game_status_data = dbFetchAssoc($sql_game_status);

    $res = 0;
    if ($game_status_data['value']) {
        // echo 1;
        // Calculate total bet and total amount
        $current_id = currentid();
        $sql_total_bet = dbQuery("SELECT COUNT(*) AS total_bet FROM tabl_aviator_betting WHERE game_id = $current_id");
        $row_total_bet = dbFetchAssoc($sql_total_bet);
        $total_bet = $row_total_bet['total_bet'];

        $sql_total_amount = dbQuery("SELECT SUM(amount) AS total_amount FROM tabl_aviator_betting WHERE game_id = $current_id");
        $row_total_amount = dbFetchAssoc($sql_total_amount);
        $total_amount = $row_total_amount['total_amount'];

        if ($total_bet == 0) {
            $res = rand(8, 11);
        } else {
            $randomresult = array(1.1, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9);
            $res = $randomresult[rand(0, 8)];
        }

        $status = true;
        $result = $res;
        $response = array('status' => $status, 'result' => $result);

        // Return JSON response
        return json_encode($response, JSON_PRETTY_PRINT);
    }

    $res = rand(8, 11);
    $status = true;
    $result = $res;
    $response = array('status' => $status, 'result' => $result);

    // Return JSON response
    return json_encode($response, JSON_PRETTY_PRINT);
}



echo increamentor();

// echo 10;