<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 3;
$sub_page = 30;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

// echo number_format(1200, 2);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Recharge
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">

    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Recharge</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

                                    <div class="widget-content widget-content-area br-6">
                                        <h4>Recharge</h4>
                                        <!-- <a href="add_plans.php"><button class="btn btn-primary mb-2">Add New</button></a> -->
                                        <div class="table-responsive mb-4 mt-4">
                                            <table id="zero-config" class="table table-hover" style="width:100%">
                                                <thead>
                                                    <tr>
                                                        <th>S. No.</th>
                                                        <th>Date</th>
                                                        <th>Name</th>
                                                        <th>Phone No.</th>
                                                        <!-- <th>Coins</th> -->
                                                        <th>Recharge Amount</th>

                                                        <th>Type</th>
                                                        <th>Pay Type</th>
                                                        <th>Amount Paid</th>
                                                        <th>Transaction No.</th>
                                                        <th>Status</th>
                                                        <!-- <th>Edit</th> -->
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $sel = dbQuery("SELECT * FROM tabl_deposits ORDER BY id DESC");
                                                    $i = 1;
                                                    while ($res = dbFetchAssoc($sel)) {
                                                        $query = dbQuery("SELECT * FROM  `tabl_user` WHERE  id =  '" . $res['user_id'] . "' ");
                                                        $row = dbFetchAssoc($query);

                                                        $pay_type = "INR";
                                                        $type = "Bank";
                                                        $pay_amt = $res['amount'];

                                                        $deposit_id = $res['deposit_id'];
                                                        $query = dbQuery("SELECT * FROM `tabl_rechargesetting` WHERE id='$deposit_id' ORDER BY `id` DESC");

                                                        if ($payment_type = dbFetchAssoc($query)) {
                                                            $pay_type = $payment_type['pay_type'];

                                                            if ($payment_type['type'] == 'bank') {
                                                                $type = 'Bank';
                                                                $pay_amt = $res['amount'];
                                                            } else if ($payment_type['type'] == 'online') {
                                                                $type = 'Razorpay';
                                                                $pay_amt = $res['amount'];
                                                            } else {
                                                                if ($pay_type == 'INR') {
                                                                    $type = 'UPI';
                                                                    $pay_amt = $res['amount'];
                                                                } else {
                                                                    $type = 'USDT';
                                                                    $pay_amt = number_format($res['amount'] / USDT_RATE, 2);
                                                                }
                                                            }
                                                        }

                                                        ?>
                                                        <tr>
                                                            <td>
                                                                <?php echo $i; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $res['date']; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $row['name']; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $row['phone']; ?>
                                                            </td>
                                                            <!-- <td><?php echo $res['amount']; ?> Coins</td> -->
                                                            <td>
                                                                <?php echo $res['amount']; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $type; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $pay_type; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $pay_amt; ?>
                                                            </td>
                                                            <td>
                                                                <?php echo $res['ref_num']; ?>
                                                            </td>

                                                            <td>
                                                                <?php if ($res['status'] == 0) { ?>
                                                                    <div class="badge badge-warning">Pending</div>
                                                                <?php } else if ($res['status'] == 1) { ?>
                                                                        <div class="badge badge-success">Approved</div>
                                                                <?php } else { ?>
                                                                        <div class="badge badge-danger">Rejected</div>
                                                                <?php }
                                                                ?>
                                                            </td>

                                                            <!-- <td><?php if ($res['status'] == 0) { ?>
                                                                    <a href="javascript:void(0)" onClick="change_status('tabl_vendor',1,<?php echo $res['id'] ?>)">
                                                                        <div class="badge badge-warning">Disable</div>
                                                                    </a>
                                                                <?php } else { ?>
                                                                    <a href="javascript:void(0)" onClick="change_status('tabl_vendor',0,<?php echo $res['id'] ?>)">
                                                                        <div class="badge badge-success">Active</div>
                                                                    </a>
                                                                <?php }
                                                            ?>
                                                            </td> -->


                                                            <!-- <td><a href="edit_Recharge.php?id=<?php echo $res['id']; ?>"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-edit">
                                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                                            </svg></a></td> -->

                                                        </tr>
                                                        <?php $i++;
                                                    } ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->
    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                    "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [8, 10, 20, 50],
            "pageLength": 10
        });
    </script>
    <!-- END PAGE LEVEL SCRIPTS -->
    <script>
        function delete_vendor(tabl, id) {
            var retVal = confirm("Are you sure want to delete.");
            if (retVal == true) {
                $.ajax({
                    url: 'ajax/delete.php',
                    type: 'post',
                    data: {
                        'tabl': tabl,
                        'row_id': id
                    },
                    success: function (data) {
                        //alert(data);
                        if (data == 1) {
                            location.reload();
                        }
                    },
                });
            } else {
                return false;
            }
        }
    </script>
    <script>
        function change_status(tabl, val, row_id) {
            if (val) {
                var retVal = confirm("Are you sure want to activate this Vendor.");
            } else {
                var retVal = confirm("Are you sure want to deactivate this Vendor.");
            }


            if (retVal == true) {
                $.ajax({
                    url: 'ajax/activate.php',
                    type: 'post',
                    data: {
                        'tabl': tabl,
                        'val': val,
                        'row_id': row_id
                    },
                    success: function (data) {
                        //alert(data);
                        if (data == 1) {
                            product_status('tabl_products', val, row_id);
                        }
                    },
                });
            } else {
                return false;
            }
        }

        function product_status(tabl, val, row_id) {

            $.ajax({
                url: 'ajax/activate_product.php',
                type: 'post',
                data: {
                    'tabl': tabl,
                    'val': val,
                    'row_id': row_id
                },
                success: function (data) {
                    //alert(data);
                    if (data == 1) {
                        location.reload();
                    }
                },
            });
        }
    </script>

    <script>
        function set_homepage(tabl, val, row_id) {
            $.ajax({
                url: 'ajax/global_show.php',
                type: 'post',
                data: {
                    'tabl': tabl,
                    'val': val,
                    'row_id': row_id
                },
                success: function (data) { },
            });
        }
    </script>

</body>

</html>