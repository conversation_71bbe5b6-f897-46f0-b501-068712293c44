<?php
session_start();
include ('../admin/lib/db_connection.php');
// include ('./lib/resize-class.php');

$user_id = $_SESSION['user_id'];

// echo $game_type;

$game_type = $_REQUEST['game_type'];
$name = $_REQUEST['name'];
$time_counter = $_REQUEST['time_counter'];
// $color = $_REQUEST['color'];


if ($game_type == 1) {
  $time = 1;
} else if ($game_type == 2) {
  $time = 3;
} else if ($game_type == 3) {
  $time = 5;
} else if ($game_type == 4) {
  $time = 10;
}

if ($name == 'small') {
  $bg_color = 'background: linear-gradient(133deg, #013f9a, #0a58caa6);';
} else if ($name == 'big') {
  $bg_color = 'background: linear-gradient(133deg, #f17714, #ff8c42);';
} else if ($name == '0') {
  $bg_color = 'background: linear-gradient(133deg, #ff5507, #ff5507, white);';
} else if ($name == '5') {
  $bg_color = 'background: linear-gradient(133deg, #19752b, #19752b, white);';
} else if (in_array($name, ['green', 1, 3, 7, 9])) {
  $bg_color = 'background: linear-gradient(133deg, #19752b, #44a362);';
} else if ($name == 'white') {
  $bg_color = 'background: linear-gradient(133deg, #013f9a, #0a58caa6);';
} else if (in_array($name, ['orange', 2, 4, 6, 8])) {
  $bg_color = 'background: linear-gradient(133deg, #ff5507, #ff8c42);';
}


// if ($color == 'light_blue') {
//   $bg_color = 'background: linear-gradient(133deg, #013f9a, #0a58caa6);';
// } else if ($color == 'light_orange') {
//   $bg_color = 'background: linear-gradient(133deg, #f17714, #ff8c42);';
// } else if ($color == 'ow') {
//   $bg_color = 'background: linear-gradient(133deg, #ff5507, white);';
// } else if ($color == 'gw') {
//   $bg_color = 'background: linear-gradient(133deg, #19752b, white);';
//   $bg_color = 'background: linear-gradient(133deg, #19752b, #317cee);';
// } else if ($color == 'green') {
//   $bg_color = 'background: linear-gradient(133deg, #19752b, #44a362);';
// } else if ($color == 'white') {
//   $bg_color = 'background: linear-gradient(133deg, #013f9a, #0a58caa6);';
// } else if ($color == 'orange') {
//   $bg_color = 'background: linear-gradient(133deg, #ff5507, #ff8c42);';
// }

?>

<style>
  .heading_div {
    border-radius: 19px 19px 0px 0px;
    color: white;
    position: relative;
  }

  .offcanvas-header {}

  .arrow-down {
    position: absolute;
    bottom: -10px;
    /* Adjust this value as needed to position the arrow */
    left: calc(50% - 10px);
    /* Adjust this value as needed to horizontally center the arrow */
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #fff;
    /* Change the color if needed */
  }

  .multiplier {
    background-color: #e7e7e7;
    padding: 4px 8px;
    color: #6d6969;
  }

  .multiplier_btns .active {
    background-color: #0a58ca;
    color: #fff
  }


  .amount_btn {
    background-color: #e7e7e7;
    padding: 4px 8px;
    color: #6d6969;
  }

  .amount_btns .active {
    background-color: #0a58ca;
    color: #fff
  }

  /* background-color: #e7e7e7;
  padding: 4px 8px;
  color: #6d6969; */
</style>

<div class="offcanvas offcanvas-bottom" style="height: auto; height: auto; border-radius: 20px 20px 0px 0px"
  tabindex="-1" id="offcanvasBottom" aria-labelledby="offcanvasBottomLabel">

  <form action="" id="bet_form">

    <div class="offcanvas-header heading_div" style="<?php echo $bg_color; ?>">
      <!-- <h6 style="width: 100%;text-align: center">Wingo
        <?php echo $time; ?> min
      </h6> -->

      <h6 style="width: 100%;text-align: center">Colour Prediction
        <?php echo $time; ?> min
      </h6>
      <div class="arrow-down"></div>
    </div>

    <div class="offcanvas-body small" style="overflow-y: unset;">

      <!-- <div style="background-color: gray"> -->

      <!-- <div class="text-center" style="background: linear-gradient(to bottom right, rgb(112, 175, 247) 50%, rgb(236, 236, 232) 50%); height: 100px">

hello

</div> -->
      <!-- <hr /> -->

      <button style="
            width: 100%;
            border: none;
            color: #fff;
            margin-bottom: 12px;
            border-radius: 20px;
            padding: 5px;
            <?php echo $bg_color; ?>
            /* background: linear-gradient(133deg, #013f9a, #0a58caa6); */
          ">
        Select
        <?php echo ucfirst($name); ?>
      </button>
      <div class="d-flex justify-content-between mt-3">
        <h6>Balance</h6>

        <h6 class="amount_btns">
          <!-- <span class="amount_btn no_select pointer active" onclick="updateAmount(1);" data-amount="1">1</span> -->
          <!-- &nbsp; -->
          <span class="amount_btn no_select pointer active" onclick="updateAmount(10);" data-amount="10">10</span>
          &nbsp;
          <span class="amount_btn no_select pointer" onclick="updateAmount(100);" data-amount="100">100</span>
          &nbsp;
          <span class="amount_btn no_select pointer" onclick="updateAmount(1000);" data-amount="1000">1000</span>
        </h6>

      </div>
      <div class="d-flex justify-content-between mt-3">
        <h6>Quantity</h6>
        <h6>
          <span class="no_select pointer" style="background-color: #0a58ca; padding: 4px 8px; color: #fff"
            onclick="updateQuantity(-1);">-</span>
          &nbsp;
          <!-- <span style="
                background-color: #ffffff;
                padding: 3px 27px;
                color: #2b2929;
                border: 1px solid #8b8181;
              ">1</span> -->

          <input type="text" class="text-center" id="quantity"
            style="background-color: #ffffff;color: #2b2929;border: 1px solid #8b8181;width: 80px;padding: 5px 10px;"
            value="1">

          <span class="no_select pointer" style="background-color: #0a58ca; padding: 4px 8px; color: #fff"
            onclick="updateQuantity(1);">+</span>
          <span></span>
        </h6>
      </div>
      <div class="text-end mt-4">
        <h6 class="multiplier_btns">
          <span class="multiplier no_select pointer active" onclick="updateMultiplier(1);" data-multiplier="1">x1</span>
          &nbsp;
          <span class="multiplier no_select pointer" onclick="updateMultiplier(5);" data-multiplier="5">x5</span>
          &nbsp;
          <span class="multiplier no_select pointer" onclick="updateMultiplier(10);" data-multiplier="10">x10</span>
          &nbsp;
          <span class="multiplier no_select pointer" onclick="updateMultiplier(20);" data-multiplier="20">x20</span>
          &nbsp;
          <span class="multiplier no_select pointer" onclick="updateMultiplier(50);" data-multiplier="50">x50</span>
          &nbsp;
          <span class="multiplier no_select pointer" onclick="updateMultiplier(100);" data-multiplier="100">x100</span>
        </h6>
      </div>

      <div class="form-check mt-3">
        <input class="form-check-input" type="checkbox" value="1" id="flexCheckChecked" name="presalerule" checked />
        <label class="form-check-label" for="flexCheckChecked">
          I agree &nbsp;
          <span style="color: #0a58ca">
            << pre-sale rules>>
          </span>
        </label>
      </div>
    </div>
    <div class="offcanvas-header" style="padding: 0px;<?php echo $bg_color; ?>">

      <button type="button" class="btn-secondary" data-bs-dismiss="offcanvas" id="offcanvas_close" aria-label="Close"
        style="
            /* background: #111147; */
            background: #25253c;
            color: #fff;
            padding: 9px 20px;
            width: 50%;
            border-radius: 0px;
            ">
        Cancel
      </button>
      <h5 class="offcanvas-title" id="totalAmount" onclick="bet_now();"
        style="color: #fff;text-align: center;font-size: 13px;width: 50%;height: 100%;padding: 10px 20px;cursor: pointer;">
        Total amount &#8377; 10.00
      </h5>
    </div>

    <input type="hidden" name="user_id" id="user_id" class="form-control" value="<?php echo $user_id; ?>">
    <input type="hidden" name="inputgameid" id="inputgameid" class="form-control"
      value="<?php echo get_wingo_gameid($game_type); ?>">
    <input type="hidden" name="game_type" id="game_type" class="form-control" value="<?php echo $game_type; ?>">

    <input type="hidden" name="counter" id="counter" class="form-control" value="<?php echo $time_counter; ?>">
    <input type="hidden" name="value" id="value" class="form-control" value="<?php echo $name; ?>">
    <input type="hidden" name="total_amount" id="total_amount" value="10">


  </form>

</div>