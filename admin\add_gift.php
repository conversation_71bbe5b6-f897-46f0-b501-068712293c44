<?php
session_start();
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
$page = 11;
$sub_page = 110;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:s:i');

if (isset($_REQUEST['submit'])) {

    $user_id = mysqli_real_escape_string($con, $_REQUEST['user_id']);
    $gift_code = mysqli_real_escape_string($con, $_REQUEST['gift_code']);
    $amount = mysqli_real_escape_string($con, $_REQUEST['amount']);
    $description = mysqli_real_escape_string($con, $_REQUEST['description']);
    // $type = 'public';

    $sel_gift = dbQuery("SELECT * FROM tabl_gift WHERE gift_code='" . $gift_code . "'");
    if (!$res_gift = dbFetchAssoc($sel_gift)) {

        $status = dbQuery("INSERT INTO tabl_gift SET user_id='" . $user_id . "',gift_code='" . $gift_code . "',amount='" . $amount . "',description='" . $description . "',status=0, date='$date'");

        if ($status) {

            if (isset($_REQUEST['notify']) && $_REQUEST['notify'] == 1) {

                $title = "Gift card received";
                // $notice_description = "";
                $type = 'gift';

                $status = dbQuery("INSERT INTO tabl_notification SET user_id='" . $user_id . "',title='" . $title . "',description='" . $description . "',type='" . $type . "',status=0,data='$gift_code', date='$date'");


                if ($status) {
                    echo '<script>alert("Gift Added & notification sent!");window.location.href="gift.php"</script>';
                } else {
                    echo '<script>alert("Gift Added!");window.location.href="gift.php"</script>';
                }
            } else {
                echo '<script>alert("Gift Added!");window.location.href="gift.php"</script>';
            }

        } else {
            echo '<script>alert("Something Went Wrong!");window.location.href="gift.php"</script>';
        }
    } else {
        echo '<script>alert("Duplicate gift code, please enter a different code!");window.location.href="add_gift.php"</script>';
    }
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Add Gift </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN THEME GLOBAL STYLES -->
    <link href="plugins/flatpickr/flatpickr.css" rel="stylesheet" type="text/css">
    <link href="plugins/noUiSlider/nouislider.min.css" rel="stylesheet" type="text/css">
    <!-- END THEME GLOBAL STYLES -->

    <!--  BEGIN CUSTOM STYLE FILE  -->

    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/scrollspyNav.css" rel="stylesheet" type="text/css" />
    <link href="plugins/flatpickr/custom-flatpickr.css" rel="stylesheet" type="text/css">
    <link href="plugins/noUiSlider/custom-nouiSlider.css" rel="stylesheet" type="text/css">
    <link href="plugins/bootstrap-range-Slider/bootstrap-slider.css" rel="stylesheet" type="text/css">
    <!--  END CUSTOM STYLE FILE  -->

    <!-- Summernote CSS -->
    <link rel="stylesheet" href="assets/summernote/css/summernote.min.css">


</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="blogs.php">Gift</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Add</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info"
                                        enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Add Gift</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">


                                                                <div class="form-row">
                                                                    <div class="form-group col-md-12">
                                                                        <label for="gift_code"><strong>Gift
                                                                                Code</strong></label>
                                                                        <input type="text" class="form-control"
                                                                            id="gift_code" name="gift_code"
                                                                            placeholder="Gift Code" required>
                                                                    </div>

                                                                    <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Select
                                                                                User</strong></label>
                                                                        <!-- <input type="text" class="form-control"
                                                                            id="Name" name="title" placeholder="Title"
                                                                            required> -->

                                                                        <select name="user_id" id="user_id"
                                                                            class="form-control">
                                                                            <option value="0">All User</option>

                                                                            <?php
                                                                            $sel_user = dbQuery("SELECT * FROM tabl_user ORDER BY id");
                                                                            while ($res_user = dbFetchAssoc($sel_user)) {
                                                                                ?>
                                                                                <option
                                                                                    value="<?php echo $res_user['id']; ?>">
                                                                                    <?php echo $res_user['name']; ?> - <?php echo $res_user['email']; ?>
                                                                                </option>
                                                                                <?php
                                                                            }
                                                                            ?>
                                                                        </select>
                                                                    </div>


                                                                    <div class="form-group col-md-6">
                                                                        <label
                                                                            for="amount"><strong>Amount</strong></label>
                                                                        <input type="text" class="form-control"
                                                                            id="amount" name="amount"
                                                                            placeholder="Amount" required>
                                                                    </div>

                                                                    <div class="form-group col-md-6">
                                                                        <label for="notify"><strong>
                                                                                Notification</strong></label>
                                                                        <br>
                                                                        <input type="checkbox" class="" id="notify"
                                                                            name="notify" placeholder="notify" value="1"
                                                                            style="width: 15px; height: 15px">
                                                                        <label for="notify"><strong>Send
                                                                                Notification</strong></label>
                                                                    </div>

                                                                    <!-- <div class="form-group col-md-6">
                                                                        <label
                                                                            for="limit"><strong>Limit</strong></label>
                                                                        <input type="text" class="form-control"
                                                                            id="limit" name="limit" placeholder="Limit"
                                                                            required>
                                                                    </div> -->

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label
                                                                                for="description"><strong>Description</strong></label>
                                                                            <textarea class="form-control"
                                                                                id="description" name="description"
                                                                                placeholder="Description" required
                                                                                rows="4"></textarea>
                                                                        </div>

                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit"
                                                                                class="btn btn-secondary mb-4 mr-2">Submit</button>
                                                                        </div>

                                                                    </div>



                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="plugins/jquery-ui/jquery-ui.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="plugins/highlight/highlight.pack.js"></script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="assets/js/scrollspyNav.js"></script>
    <script src="plugins/flatpickr/flatpickr.js"></script>
    <script src="plugins/noUiSlider/nouislider.min.js"></script>

    <script src="plugins/flatpickr/custom-flatpickr.js"></script>
    <script src="plugins/noUiSlider/custom-nouiSlider.js"></script>
    <script src="plugins/bootstrap-range-Slider/bootstrap-rangeSlider.js"></script>

    <!-- Summernote lite javascript -->
    <script src="assets/summernote/js/summernote-lite.min.js"></script>

    <script>
        function displayResult() {

            var i = document.getElementById("num").value;

            document.getElementById("classes").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><select name="class_type_' + i + '" id="class_type_' + i + '" class="form-control"><option value="0">SELECT</option><option value="1">Pre-Recorded</option><option value="2">Free Classes</option></select></td><td><select name="type_' + i + '" id="type_' + i + '" class="form-control" onChange="set_class_type(' + i + ',this.value)"><option value="0">SELECT</option><option value="1">PDF</option><option value="2">URL</option></select></td><td><input type="text" name="file_' + i + '" id="doc_type_' + i + '"  class="form-control" accept="application/pdf"></td></tr>';
            i++;

            var num = document.getElementById("num").value = i;

        }
    </script>

    <script>
        function isNumber(evt) {
            var iKeyCode = (evt.which) ? evt.which : evt.keyCode
            if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
                return false;

            return true;
        }

        function isDecimal(evt, obj) {

            var charCode = (evt.which) ? evt.which : event.keyCode
            var value = obj.value;
            var dotcontains = value.indexOf(".") != -1;
            if (dotcontains)
                if (charCode == 46) return false;
            if (charCode == 46) return true;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
    </script>
    <script>
        function set_sub_cat(cat_id) {
            $.ajax({
                url: 'ajax/get_sub_category.php',
                type: 'post',
                data: {
                    'cat_id': cat_id
                },
                success: function (data) {
                    if (data != "") {
                        $("#sub_cat_id").html(data);
                    } else {
                        $("#sub_cat_id").html('<option value="0">SELECT</option>');

                    }
                },
            });

        }
    </script>
    <script>
        function set_class_type(id, val) {
            if (val == 1) {
                $("#doc_type_" + id).attr('type', 'file');
            } else {
                $("#doc_type_" + id).attr('type', 'text');
            }
        }
    </script>

    <script>
        $('#type').summernote({
            placeholder: 'Enter type Here...',
            tabsize: 2,
            height: 200,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ]
        });
    </script>
</body>

</html>