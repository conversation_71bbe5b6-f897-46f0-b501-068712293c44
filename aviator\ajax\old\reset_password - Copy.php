<?php
session_start();
include('../admin/lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');



if (isset($_REQUEST['token'])) {

  $token = $_REQUEST['token'];

  $new_password = $_REQUEST['new_password'];
  $con_password = $_REQUEST['con_password'];

  if ($new_password == $con_password) {

    $sel = dbQuery("SELECT * FROM tabl_user WHERE token='" . $token . "'");
    $res = dbFetchAssoc($sel);

    $num = dbNumRows($sel);
    if ($num > 0) {

      $password = password_hash($new_password, PASSWORD_BCRYPT);

      $upd = dbQuery("UPDATE tabl_user Password SET password='" . $password . "',token='' WHERE id='" . $res['id'] . "'");
      if ($upd) {
        echo 1;
      } else {
        echo 0;
      }
    } else {
      echo 0;
    }

  } else {
    echo 2;
    
  }
} else {
  echo 0;
}
