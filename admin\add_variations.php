<?php 
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=2;
$sub_page=24;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
if(isset($_REQUEST['submit'])){	

	if($_REQUEST['id']==1){
        dbQuery("INSERT INTO tabl_product_variation_value SET v_id='".$_REQUEST['id']."',v_value='".$_REQUEST['v_value']."',color='".$_REQUEST['color']."',date_added='".$date."'");
	}else{
	dbQuery("INSERT INTO tabl_product_variation_value SET v_id='".$_REQUEST['id']."',v_value='".$_REQUEST['v_value']."',date_added='".$date."'");
 }	  
echo '<script>alert("Variation Added!");window.location.href="add_variations.php?id='.$_REQUEST['id'].'"</script>';
}

$sel=dbQuery("SELECT * FROM tabl_product_variation WHERE id='".$_REQUEST['id']."'");
$res=dbFetchAssoc($sel);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Add Variations </title>
    <link rel="icon" type="image/x-icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg"/>
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    
    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
    
    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
	<script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

</head>
<body class="alt-menu sidebar-noneoverflow">

  <?php include('inc/__header.php');?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php');?>
        <!--  END TOPBAR  -->
        
        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">                
<nav class="breadcrumb-one" aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
        <li class="breadcrumb-item"><a href="variation.php">Variations</a></li>
        <li class="breadcrumb-item active"><a href="javascript:void(0);">Add</a></li>

    </ol>
</nav>
                <div class="account-settings-container layout-top-spacing">                       
                    <div class="widget-content widget-content-area info">
					    <h4 class="">Add Variations</h4>
						     <h6>Variation Name: <span style="color:red"><?php echo $res['name'];?></span></h6>
                                    <form class="form-inline justify-content-center" method="post">
                                        <label class="sr-only" for="inlineFormInputName2">Name</label>
                                        <input type="text" class="form-control mb-2 mr-sm-2" name="v_value" id="inlineFormInputName2" placeholder="Enter Value" required>
									 
                                       <?php if($res['id']==1){?>
									      <label class="sr-only" for="inlineFormInputName2">Color</label>
                                        <input type="color" class="form-control mb-2 mr-sm-2" id="colorPicker" placeholder="Enter Color" name="color" value="#ff0000" style="width: 129px;" required>
											 <?php }?>									   
										
                                         <button type="submit" name="submit" class="btn btn-primary mb-2">Submit</button>
                                    </form>
									
				    <div class="table-responsive mb-4 mt-4">
                                <table id="zero-config" class="table table-hover" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>ID</th>                                            
                                            <th>Date</th>
											<th>Name</th>
                                            <?php if($res['id']==1){?>
											<th>Color</th>               
                                            <?php } ?>											
                                            <th>Edit</th>
                                            <th>Delete</th>                                            
                                        </tr>
                                    </thead>
                                    <tbody>
                                       <?php 
									   $sel=dbQuery("SELECT * FROM tabl_product_variation_value WHERE v_id='".$_REQUEST['id']."' ORDER BY id DESC");
										$i=1;
										while($res=dbFetchAssoc($sel)){?> 
                                        <tr>
                                            <td><?php echo $i;?></td>
											<td><?php echo date('d-m-Y',strtotime($res['date_added']));?></td>
											<td><?php echo $res['v_value'];?></td>
											<?php if($res['v_id']==1){?>
											<td><div style="background-color:<?php echo $res['color'];?>;width: 35px;height: 28px;"></div>   
											</td>											
                                            <?php } ?>	
											
											<td><a href="edit_variations.php?v_id=<?php echo $res['v_id']?>&id=<?php echo $res['id'];?>"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-edit"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg></a></td>  
                                            
                                             <td><a href="javascript:void(0)" onClick="delete_variations(<?php echo $res['id'] ?>)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg></a></td>  
                                                                                      
                                        </tr>
                                        <?php $i++; } ?>
                                    </tbody>                                    
                                </table>
                            </div>					
               </div>
 <?php include('inc/__footer.php');?>
          </div>
          
        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    
    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>
</html>
<script>
 CKEDITOR.replace( 'description' );
 </script>
 <script>
    function delete_variations(id)
	{
	var retVal = confirm("Are you sure want to delete.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/delete_variations.php',
	  type:'post',
	  data:{'id':id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   }
 	
	}
    </script>