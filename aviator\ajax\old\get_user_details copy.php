<?php
ob_start();
session_start();
if ($_SESSION['user_id'] == "") {
    header("location:login.php");
    exit();
}

include ('../../admin/lib/db_connection.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');


// Function to send JSON response
function json_response($data, $isSuccess, $message)
{
    $res = array(
        "data" => $data,
        "isSuccess" => $isSuccess,
        "message" => $message
    );
    return json_encode($res);
}


// Function to fetch user details
function get_user_detail()
{
    // Initialize variables
    $data = "";
    $isSuccess = false;
    $message = "Something went wrong!";
    $notification = "";
    $avatar = "../assets/img/profile_img/default.png";

    // Check if user is logged in
    if (!isset ($_SESSION['user_id']) || empty ($_SESSION['user_id'])) {
        return json_response($data, $isSuccess, "User not logged in");
    }

    // Fetch user details from database
    $sel_user = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");

    // If user details found
    if ($res_user = dbFetchAssoc($sel_user)) {
        $avatar = "../assets/img/profile_img/" . $res_user['profile_img'];
        $data = array(
            "username" => $res_user['id'],
            "user_email" => $res_user['email'] != '' ? $res_user['email'] : $res_user['phone'],
            "avatar" => $avatar,
            "notification" => $notification
        );
        $message = "Success";
        $isSuccess = true;
    } else {
        $message = "User data not found";
    }

    // Return JSON response
    return json_response($data, $isSuccess, $message);
}


// Example usage:
// echo "<pre>";
echo get_user_detail();
// echo "/<pre>";
?>