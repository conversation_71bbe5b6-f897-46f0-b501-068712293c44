<?php
session_start();
include('../lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");
$date = date("Y-m-d H:i:s");

if (isset($_REQUEST['val'])) {
    $table_name = $_REQUEST['tabl'];
    $user_id = $_REQUEST['user_id'];
    $recharge_amount = $_REQUEST['amount'];


    if ($_REQUEST['val'] == 1) {

        $sel3 = dbQuery("INSERT INTO tabl_walletsummery SET user_id='$user_id', order_id='" . $_REQUEST['row_id'] . "', amount='$recharge_amount', type='credit', actiontype='recharge~" . $_REQUEST['row_id'] . "', date='$date'");
        update_wallet($user_id, $recharge_amount, 'credit');

        $status = dbQuery("UPDATE $table_name SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['row_id'] . "'");

        dbQuery("INSERT INTO `tabl_notification` SET `user_id`='$user_id', `type`='recharge', `title`='Recharge Approved', `description`='Your account has arrived ₹$recharge_amount', data='$recharge_amount', `status`='0', `date`='$date'");



        // Check if this is the first recharge for the user
        $check_first_recharge = dbQuery("SELECT COUNT(*) as total_recharges FROM tabl_walletsummery WHERE user_id='$user_id' AND actiontype LIKE 'recharge%'");
        $first_recharge = dbFetchAssoc($check_first_recharge);


        // Proceed if it's the first recharge and the amount is greater than 500
        // if ($first_recharge['total_recharges'] == 0 && $recharge_amount > 500) {

        if ($first_recharge['total_recharges'] <= 1) {
            // Fetch self and invite bonuses
            $res_setting = dbQuery("SELECT self_bonus, invite_bonus FROM tabl_settings");
            $res_setting = dbFetchAssoc($res_setting);
            // $self_bonus = $res_setting['self_bonus'];
            // $invite_bonus = $res_setting['invite_bonus'];

            $self_bonus = SELF_BONUS;
            $invite_bonus = INVITE_BONUS;

            // Apply self bonus
            if ($self_bonus > 0) {
                $sql1 = dbQuery("INSERT INTO `tabl_walletsummery`(`user_id`, `order_id`, `amount`, `game`, `type`, `actiontype`, `date`) VALUES ('$user_id', '0', '$self_bonus', '', 'credit', 'self_bonus', '$date')");
                if ($sql1) {
                    update_wallet($user_id, $self_bonus, 'credit');
                }
            }

            $userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
            if ($user = dbFetchAssoc($userq)) {
                $ref_code = $user["ref_code"];

                // Apply invite bonus to the upliner
                $sel_user3 = dbQuery("SELECT * FROM tabl_user WHERE own_code='$ref_code'");
                if ($res_user3 = dbFetchAssoc($sel_user3)) {
                    if ($invite_bonus > 0) {
                        $upliner_id = $res_user3['id'];
                        $sql2 = dbQuery("INSERT INTO `tabl_walletsummery`(`user_id`, `order_id`, `amount`, `game`, `type`, `actiontype`, `date`) VALUES ('$upliner_id', '$user_id', '$invite_bonus', '', 'credit', 'invitation_bonus', '$date')");
                        if ($sql2) {
                            update_wallet($upliner_id, $invite_bonus, 'credit');
                        }
                    }
                }
            }
        }
    } else {
        $status = dbQuery("UPDATE $table_name SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['row_id'] . "'");

        dbQuery("INSERT INTO `tabl_notification` SET `user_id`='$user_id', `type`='recharge', `title`='Recharge Rejected', `description`='Your recharge request has been rejected', data='$recharge_amount', `status`='0', `date`='$date'");
    }

    if ($status) {
        echo '1';
    }
}
