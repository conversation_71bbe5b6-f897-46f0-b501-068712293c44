<?php
ob_start();
session_start();
include ("../admin/lib/db_connection.php");
// include ("./lib/functions.php");

@$user_id = $_SESSION['user_id'];
@$game_type = $_POST['game_type'];
@$value = $_POST['value'];
@$counter = $_POST['counter'];
@$inputgameid = $_POST['inputgameid'];
@$total_amount = $_POST['total_amount'];
@$presalerule = $_POST['presalerule'];


if ($user_id == "" || $game_type == "" || $inputgameid == "" || $total_amount == "" || $value == "") {
  echo "2";
  //check empty
} else {
  if ($game_type == 1 ? $counter < 10 : $counter < 30) {
    echo "3";
    //check counter
  } else if ($total_amount == 0) {
    echo "4";
    //check if amount 0	
  } else if ($total_amount < 10) {
    echo "5";
    //check if amount below 10
  } else {
    $wallet_balance = check_wallet($user_id);
    if ($wallet_balance < $total_amount) {
      echo "6";
    } else {
      $sql = dbQuery("INSERT INTO `tabl_wingonew_betting` (`user_id`, `game_id`, `game_type`,`value`,`amount`,`tab`,`acceptrule`) VALUES ('" . $user_id . "','" . $inputgameid . "','" . $game_type . "','" . $value . "','" . $total_amount . "','','" . $presalerule . "')");

      //=====================transaction==================================================
      $sql = dbQuery("INSERT INTO `tabl_order`(`user_id`,`game`,`transactionid`,`amount`,`status`) VALUES ('" . $user_id . "','wingonew','" . $inputgameid . "','" . $total_amount . "','1')");
      @$order_id = dbInsertId();

      $sql3 = dbQuery("INSERT INTO `tabl_walletsummery`(`user_id`,`order_id`,`amount`,`game`,`type`,`actiontype`) VALUES ('" . $user_id . "','" . $order_id . "','" . $total_amount . "','wingonew','debit','join')");

      update_wallet($user_id, $total_amount, 'debit');

      // $walletbalance = wallet($con, 'amount', $user_id);
      // $finalbalanceDebit = $walletbalance - $total_amount;
      // $sqlwallet = dbQuery("UPDATE `tabl_wallet` SET `amount` = '" . $finalbalanceDebit . "' WHERE `user_id`= '" . $user_id . "'");

      //=====================transaction end==============================================
      // // userpromocode($con, $user_id, user($con, 'code', $user_id), $total_amount, $inputgameid); //===bonus calculation


      echo "1~" . check_wallet($user_id);
    }

  }
}
